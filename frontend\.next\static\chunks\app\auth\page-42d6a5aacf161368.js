(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[365],{9342:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N});var r=a(95155),t=a(12115),l=a(62177),n=a(90221),i=a(4607),o=a(17607),d=a(81838),c=a(93176),m=a(66146),u=a(58096),x=a(81495),p=a(82842),j=a(59434),h=a(35695),f=a(56671);function g(){let e=(0,h.useRouter)(),[s,a]=(0,t.useState)(!1),[,g]=(0,p.lT)(),{control:v,handleSubmit:w,formState:{errors:b,isSubmitting:y}}=(0,l.mN)({resolver:(0,n.u)(d.X5),defaultValues:{email:"",password:""}});async function N(s){let a=await (0,j.G)("/auth/jwt/create/",s,"POST");if(200===a.status){let s=await a.json();g("access",s.access),g("refresh",s.refresh),f.o.success("تم تسجيل الدخول بنجاح"),e.push("/")}else f.o.error("فشل تسجيل الدخول، تأكد من صحة البيانات المدخلة")}return(0,r.jsxs)("form",{onSubmit:w(N),className:"space-y-6 mt-6",children:[(0,r.jsx)(l.xI,{name:"email",control:v,render:e=>{var s;let{field:a}=e;return(0,r.jsx)(c.r,{...a,type:"tel",label:"رقم الهاتف",variant:"bordered",isInvalid:!!b.email,errorMessage:null==(s=b.email)?void 0:s.message})}}),(0,r.jsx)(l.xI,{name:"password",control:v,render:e=>{var t;let{field:l}=e;return(0,r.jsx)(c.r,{...l,type:s?"text":"password",label:"كلمة المرور",variant:"bordered",isInvalid:!!b.password,errorMessage:null==(t=b.password)?void 0:t.message,endContent:(0,r.jsx)("button",{type:"button",className:"h-full pl-2",onClick:()=>a(!s),children:s?(0,r.jsx)(i.A,{size:20}):(0,r.jsx)(o.A,{size:20})})})}}),(0,r.jsx)("div",{className:"flex items-center justify-end",children:(0,r.jsx)(m.T,{as:x.h,href:"/auth/forget-password",variant:"light",className:"px-0 font-normal",children:"هل نسيت كلمة المرور؟"})}),(0,r.jsx)(m.T,{type:"submit",color:"primary",className:(0,u.cn)("w-full",y?"opacity-50":""),disabled:y,children:"تسجيل الدخول"})]})}function v(){let[,e]=(0,p.lT)(),s=(0,h.useRouter)(),[a,u]=(0,t.useState)(!1),[x,f]=(0,t.useState)(!1),[g,v]=(0,t.useState)(""),{control:w,handleSubmit:b,formState:{errors:y,isSubmitting:N}}=(0,l.mN)({resolver:(0,n.u)(d.Sd),defaultValues:{first_name:"",last_name:"",email:"",password:"",password2:""}});async function I(a){let r=await (0,j.G)("/users/",a,"POST"),t=await r.json();201!==r.status?v(Object.values(t)[0][0]):(e("access",t.access),e("refresh",t.refresh),s.push("/"))}return(0,r.jsxs)(r.Fragment,{children:[g&&(0,r.jsx)("p",{children:g[0].toUpperCase()+g.slice(1)}),(0,r.jsxs)("form",{onSubmit:b(I),className:"space-y-6 mt-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsx)(l.xI,{name:"first_name",control:w,render:e=>{var s;let{field:a}=e;return(0,r.jsx)(c.r,{...a,label:"الاسم الاول",variant:"bordered",isInvalid:!!y.first_name,errorMessage:null==(s=y.first_name)?void 0:s.message})}}),(0,r.jsx)(l.xI,{name:"last_name",control:w,render:e=>{var s;let{field:a}=e;return(0,r.jsx)(c.r,{...a,label:"اسم العائلة",variant:"bordered",isInvalid:!!y.last_name,errorMessage:null==(s=y.last_name)?void 0:s.message})}})]}),(0,r.jsx)(l.xI,{name:"email",control:w,render:e=>{var s;let{field:a}=e;return(0,r.jsx)(c.r,{...a,type:"email",label:"البريد الالكتروني",variant:"bordered",isInvalid:!!y.email,errorMessage:null==(s=y.email)?void 0:s.message})}}),(0,r.jsx)(l.xI,{name:"password",control:w,render:e=>{var s;let{field:t}=e;return(0,r.jsx)(c.r,{...t,type:a?"text":"password",label:"كلمة المرور",variant:"bordered",isInvalid:!!y.password,errorMessage:null==(s=y.password)?void 0:s.message,endContent:(0,r.jsx)("button",{type:"button",className:"h-full pl-2",onClick:()=>u(!a),children:a?(0,r.jsx)(i.A,{size:20}):(0,r.jsx)(o.A,{size:20})})})}}),(0,r.jsx)(l.xI,{name:"password2",control:w,render:e=>{var s;let{field:a}=e;return(0,r.jsx)(c.r,{...a,type:x?"text":"password",label:"تأكيد كلمة المرور",variant:"bordered",isInvalid:!!y.password2,errorMessage:null==(s=y.password2)?void 0:s.message,endContent:(0,r.jsx)("button",{type:"button",className:"h-full pl-2",onClick:()=>f(!x),children:x?(0,r.jsx)(i.A,{size:20}):(0,r.jsx)(o.A,{size:20})})})}}),(0,r.jsx)(m.T,{type:"submit",color:"primary",className:(0,j.cn)("w-full",N?"opacity-50":""),disabled:N,children:"سجل الآن"})]})]})}var w=a(90262),b=a(87036),y=a(66766);function N(){let e=(0,h.useRouter)(),s=async()=>{let s=await (0,j.G)("/auth/google/url/",null,"GET");if(200===s.status){let a=await s.json();e.push(a.url)}else f.o.error("فشل تسجيل الدخول، برجاء اعادة المحاولة")};return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"w-full max-w-md space-y-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"مرحباً بعودتك!"}),(0,r.jsx)("p",{className:"text-gray-500 mt-2",children:"مرحباً بعودتك، من فضلك ادخل بياناتك."})]}),(0,r.jsxs)(w.r,{"aria-label":"Auth options",color:"primary",variant:"underlined",className:"w-full",children:[(0,r.jsx)(b.i,{title:"تسجيل الدخول",children:(0,r.jsx)(g,{})},"login"),(0,r.jsx)(b.i,{title:"انشاء حساب ",children:(0,r.jsx)(v,{})},"signup")]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("span",{className:"w-full border-t"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,r.jsx)("span",{className:"bg-white px-2 text-gray-500",children:"او اتصل باستخدام"})})]}),(0,r.jsxs)(m.T,{variant:"ghost",onPress:s,className:"border-1 w-full border-gray-200",children:[(0,r.jsx)("span",{className:"text-xl font-bold",children:"Google"}),(0,r.jsx)(y.default,{src:"/google-icon.png",alt:"Google",width:30,height:30})]})]})})}},59434:(e,s,a)=>{"use strict";a.d(s,{G:()=>n,cn:()=>l,f:()=>i});var r=a(52596),t=a(39688);function l(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,t.QP)((0,r.$)(s))}async function n(e,s){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"GET",r=arguments.length>3?arguments[3]:void 0;return await fetch("http://localhost:8000"+e,{method:a,body:null===s?null:JSON.stringify(s),headers:{"Content-Type":"application/json",Authorization:r?"Bearer ".concat(r):""}})}let i=e=>new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})},61910:(e,s,a)=>{Promise.resolve().then(a.bind(a,9342))},81838:(e,s,a)=>{"use strict";a.d(s,{Ie:()=>n,Sd:()=>l,X5:()=>t,oW:()=>i});var r=a(71153);let t=r.Ik({email:r.Yj().email({message:"البريد الإلكتروني غير صالح"}),password:r.Yj().min(1,{message:"كلمة المرور مطلوبة"})}),l=r.Ik({first_name:r.Yj().min(2,{message:"الاسم الأول يجب أن يكون على الأقل حرفين"}),last_name:r.Yj().min(2,{message:"اسم العائلة يجب أن يكون على الأقل حرفين"}),email:r.Yj().email({message:"البريد الإلكتروني غير صالح"}),password:r.Yj().min(8,{message:"كلمة المرور يجب أن تكون على الأقل 8 أحرف"}),password2:r.Yj()}).refine(e=>e.password===e.password2,{message:"كلمات المرور غير متطابقة",path:["confirmPassword"]}),n=r.Ik({email:r.Yj().email({message:"البريد الإلكتروني غير صالح"}).optional().or(r.eu(""))}),i=r.Ik({password:r.Yj().min(8,{message:"كلمة المرور يجب أن تكون على الأقل 8 أحرف"}),confirmPassword:r.Yj()}).refine(e=>e.password===e.confirmPassword,{message:"كلمات المرور غير متطابقة",path:["confirmPassword"]})}},e=>{var s=s=>e(e.s=s);e.O(0,[477,146,688,671,833,842,943,168,161,63,797,441,684,358],()=>s(61910)),_N_E=e.O()}]);