(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[982],{35695:(e,s,t)=>{"use strict";var r=t(18999);t.o(r,"notFound")&&t.d(s,{notFound:function(){return r.notFound}}),t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},36912:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var r=t(95155),n=t(12115),o=t(35695),u=t(82842),a=t(56671);function c(e){let{}=e,s=(0,o.useRouter)(),[t,c]=(0,u.lT)(),h=(0,o.useSearchParams)().get("code");return h?((0,n.useEffect)(()=>{fetch("".concat("http://localhost:8000","/auth/google/"),{method:"POST",body:JSON.stringify({code:h}),headers:{"Content-Type":"application/json",accept:"application/json"}}).then(e=>{if(201===e.status)return e.json();a.o.error("فشل تسجيل الدخول، برجاء اعادة المحاولة"),s.push("/auth")}).then(e=>{c("access",e.access_token),c("refresh",e.refresh_token),a.o.success("تم تسجيل الدخول بنجاح"),s.refresh(),s.push("/")})},[]),(0,r.jsxs)("div",{className:"grid place-content-center h-full",children:[(0,r.jsx)("p",{className:"font-bold text-2xl",children:"جارى تسجيل الدخول..."}),";"]})):(0,o.notFound)()}},49328:(e,s,t)=>{Promise.resolve().then(t.bind(t,36912))}},e=>{var s=s=>e(e.s=s);e.O(0,[671,842,441,684,358],()=>s(49328)),_N_E=e.O()}]);