[{"G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\add-application\\page.tsx": "1", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\admin\\chats\\page.tsx": "2", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\admin\\chats\\[id]\\page.tsx": "3", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\admin\\layout.tsx": "4", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\googlecallback\\page.tsx": "5", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\layout.tsx": "6", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\page.tsx": "7", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\auth\\forget-password\\page.tsx": "8", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\auth\\layout.tsx": "9", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\auth\\page.tsx": "10", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\auth\\reset-password\\page.tsx": "11", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\layout.tsx": "12", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\authentication\\ForgetPasswordForm.tsx": "13", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\authentication\\LoginForm.tsx": "14", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\authentication\\ResetPasswordForm.tsx": "15", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\authentication\\SignUpForm.tsx": "16", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\global\\ClientProviders.tsx": "17", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\global\\Footer.tsx": "18", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\global\\Header.tsx": "19", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\AlertItemDetails\\page.tsx": "20", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\AlertSection\\index.tsx": "21", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\CallUs\\index.tsx": "22", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\Chat\\index.tsx": "23", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\EmergencyForm\\index.tsx": "24", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\ui\\animated-modal.tsx": "25", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\ui\\file-upload.tsx": "26", "G:\\Graduation project 2025\\app\\frontend\\src\\lib\\data.ts": "27", "G:\\Graduation project 2025\\app\\frontend\\src\\lib\\utils.ts": "28", "G:\\Graduation project 2025\\app\\frontend\\src\\schemas\\application.ts": "29", "G:\\Graduation project 2025\\app\\frontend\\src\\schemas\\auth.ts": "30", "G:\\Graduation project 2025\\app\\frontend\\src\\schemas\\messages.ts": "31"}, {"size": 386, "mtime": 1744025730000, "results": "32", "hashOfConfig": "33"}, {"size": 4467, "mtime": 1748194920759, "results": "34", "hashOfConfig": "33"}, {"size": 8491, "mtime": 1748194978686, "results": "35", "hashOfConfig": "33"}, {"size": 650, "mtime": 1748195001670, "results": "36", "hashOfConfig": "33"}, {"size": 1656, "mtime": 1748195040687, "results": "37", "hashOfConfig": "33"}, {"size": 2474, "mtime": 1748195054067, "results": "38", "hashOfConfig": "33"}, {"size": 2940, "mtime": 1748195079914, "results": "39", "hashOfConfig": "33"}, {"size": 834, "mtime": 1748195093986, "results": "40", "hashOfConfig": "33"}, {"size": 1530, "mtime": 1744563014000, "results": "41", "hashOfConfig": "33"}, {"size": 2764, "mtime": 1748195107810, "results": "42", "hashOfConfig": "33"}, {"size": 1104, "mtime": 1744545032000, "results": "43", "hashOfConfig": "33"}, {"size": 1132, "mtime": 1744571936000, "results": "44", "hashOfConfig": "33"}, {"size": 2131, "mtime": 1748195124220, "results": "45", "hashOfConfig": "33"}, {"size": 3871, "mtime": 1748195139271, "results": "46", "hashOfConfig": "33"}, {"size": 4777, "mtime": 1744545644000, "results": "47", "hashOfConfig": "33"}, {"size": 6575, "mtime": 1748195165318, "results": "48", "hashOfConfig": "33"}, {"size": 262, "mtime": 1732192774000, "results": "49", "hashOfConfig": "33"}, {"size": 3997, "mtime": 1748195192208, "results": "50", "hashOfConfig": "33"}, {"size": 2447, "mtime": 1748195206815, "results": "51", "hashOfConfig": "33"}, {"size": 3858, "mtime": 1748195233491, "results": "52", "hashOfConfig": "33"}, {"size": 3608, "mtime": 1744343474000, "results": "53", "hashOfConfig": "33"}, {"size": 2397, "mtime": 1735488810000, "results": "54", "hashOfConfig": "33"}, {"size": 6933, "mtime": 1744762266000, "results": "55", "hashOfConfig": "33"}, {"size": 10686, "mtime": 1744340788000, "results": "56", "hashOfConfig": "33"}, {"size": 6820, "mtime": 1744552016000, "results": "57", "hashOfConfig": "33"}, {"size": 10065, "mtime": 1735496490000, "results": "58", "hashOfConfig": "33"}, {"size": 284, "mtime": 1742069110000, "results": "59", "hashOfConfig": "33"}, {"size": 816, "mtime": 1748194852488, "results": "60", "hashOfConfig": "33"}, {"size": 1319, "mtime": 1744571042000, "results": "61", "hashOfConfig": "33"}, {"size": 2006, "mtime": 1740975140000, "results": "62", "hashOfConfig": "33"}, {"size": 195, "mtime": 1744057918000, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "553f0k", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\add-application\\page.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\admin\\chats\\page.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\admin\\chats\\[id]\\page.tsx", ["157", "158"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\admin\\layout.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\googlecallback\\page.tsx", ["159"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\layout.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\page.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\auth\\forget-password\\page.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\auth\\layout.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\auth\\page.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\auth\\reset-password\\page.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\layout.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\authentication\\ForgetPasswordForm.tsx", ["160"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\authentication\\LoginForm.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\authentication\\ResetPasswordForm.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\authentication\\SignUpForm.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\global\\ClientProviders.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\global\\Footer.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\global\\Header.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\AlertItemDetails\\page.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\AlertSection\\index.tsx", ["161"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\CallUs\\index.tsx", ["162"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\Chat\\index.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\EmergencyForm\\index.tsx", ["163", "164", "165"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\ui\\animated-modal.tsx", ["166", "167"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\ui\\file-upload.tsx", ["168", "169", "170", "171"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\lib\\data.ts", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\lib\\utils.ts", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\schemas\\application.ts", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\schemas\\auth.ts", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\schemas\\messages.ts", [], [], {"ruleId": "172", "severity": 2, "message": "173", "line": 61, "column": 5, "nodeType": "174", "endLine": 61, "endColumn": 14}, {"ruleId": "172", "severity": 2, "message": "173", "line": 110, "column": 5, "nodeType": "174", "endLine": 110, "endColumn": 14}, {"ruleId": "172", "severity": 2, "message": "173", "line": 15, "column": 5, "nodeType": "174", "endLine": 15, "endColumn": 14}, {"ruleId": "175", "severity": 2, "message": "176", "line": 9, "column": 10, "nodeType": null, "messageId": "177", "endLine": 9, "endColumn": 19}, {"ruleId": "175", "severity": 2, "message": "178", "line": 6, "column": 5, "nodeType": null, "messageId": "177", "endLine": 6, "endColumn": 17}, {"ruleId": "179", "severity": 2, "message": "180", "line": 6, "column": 14, "nodeType": "181", "messageId": "182", "endLine": 6, "endColumn": 16, "suggestions": "183"}, {"ruleId": "175", "severity": 2, "message": "184", "line": 16, "column": 10, "nodeType": null, "messageId": "177", "endLine": 16, "endColumn": 17}, {"ruleId": "175", "severity": 2, "message": "185", "line": 42, "column": 12, "nodeType": null, "messageId": "177", "endLine": 42, "endColumn": 26}, {"ruleId": "175", "severity": 2, "message": "186", "line": 89, "column": 18, "nodeType": null, "messageId": "177", "endLine": 89, "endColumn": 23}, {"ruleId": "187", "severity": 2, "message": "188", "line": 224, "column": 15, "nodeType": "174", "messageId": "189", "endLine": 224, "endColumn": 23}, {"ruleId": "190", "severity": 2, "message": "191", "line": 227, "column": 34, "nodeType": "192", "messageId": "193", "endLine": 227, "endColumn": 37, "suggestions": "194"}, {"ruleId": "175", "severity": 2, "message": "195", "line": 6, "column": 10, "nodeType": null, "messageId": "177", "endLine": 6, "endColumn": 12}, {"ruleId": "175", "severity": 2, "message": "196", "line": 7, "column": 8, "nodeType": null, "messageId": "177", "endLine": 7, "endColumn": 13}, {"ruleId": "197", "severity": 2, "message": "198", "line": 41, "column": 9, "nodeType": "199", "messageId": "200", "endLine": 41, "endColumn": 40}, {"ruleId": "197", "severity": 2, "message": "198", "line": 125, "column": 49, "nodeType": "199", "messageId": "200", "endLine": 125, "endColumn": 77}, "react-hooks/rules-of-hooks", "React Hook \"useEffect\" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?", "Identifier", "@typescript-eslint/no-unused-vars", "'useRouter' is defined but never used.", "unusedVar", "'ModalContent' is defined but never used.", "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["201", "202"], "'fetcher' is defined but never used.", "'selectedImages' is assigned a value but never used.", "'error' is defined but never used.", "@typescript-eslint/no-unsafe-function-type", "The `Function` type accepts any function-like value.\nPrefer explicitly defining any function parameters and return type.", "bannedFunctionType", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["203", "204"], "'on' is defined but never used.", "'Image' is defined but never used.", "@typescript-eslint/no-unused-expressions", "Expected an assignment or function call and instead saw an expression.", "ExpressionStatement", "unusedExpression", {"messageId": "205", "data": "206", "fix": "207", "desc": "208"}, {"messageId": "205", "data": "209", "fix": "210", "desc": "211"}, {"messageId": "212", "fix": "213", "desc": "214"}, {"messageId": "215", "fix": "216", "desc": "217"}, "replaceEmptyObjectType", {"replacement": "218"}, {"range": "219", "text": "218"}, "Replace `{}` with `object`.", {"replacement": "220"}, {"range": "221", "text": "220"}, "Replace `{}` with `unknown`.", "suggestUnknown", {"range": "222", "text": "220"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "223", "text": "224"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "object", [159, 161], "unknown", [159, 161], [6255, 6258], [6255, 6258], "never"]