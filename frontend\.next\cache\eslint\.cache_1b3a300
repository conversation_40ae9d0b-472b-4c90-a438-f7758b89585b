[{"G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\add-application\\page.tsx": "1", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\admin\\chats\\page.tsx": "2", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\admin\\chats\\[id]\\page.tsx": "3", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\admin\\layout.tsx": "4", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\googlecallback\\page.tsx": "5", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\layout.tsx": "6", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\page.tsx": "7", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\auth\\forget-password\\page.tsx": "8", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\auth\\layout.tsx": "9", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\auth\\page.tsx": "10", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\auth\\reset-password\\page.tsx": "11", "G:\\Graduation project 2025\\app\\frontend\\src\\app\\layout.tsx": "12", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\authentication\\ForgetPasswordForm.tsx": "13", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\authentication\\LoginForm.tsx": "14", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\authentication\\ResetPasswordForm.tsx": "15", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\authentication\\SignUpForm.tsx": "16", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\global\\ClientProviders.tsx": "17", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\global\\Footer.tsx": "18", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\global\\Header.tsx": "19", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\AlertItemDetails\\page.tsx": "20", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\AlertSection\\index.tsx": "21", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\CallUs\\index.tsx": "22", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\Chat\\index.tsx": "23", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\EmergencyForm\\index.tsx": "24", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\ui\\animated-modal.tsx": "25", "G:\\Graduation project 2025\\app\\frontend\\src\\components\\ui\\file-upload.tsx": "26", "G:\\Graduation project 2025\\app\\frontend\\src\\lib\\data.ts": "27", "G:\\Graduation project 2025\\app\\frontend\\src\\lib\\utils.ts": "28", "G:\\Graduation project 2025\\app\\frontend\\src\\schemas\\application.ts": "29", "G:\\Graduation project 2025\\app\\frontend\\src\\schemas\\auth.ts": "30", "G:\\Graduation project 2025\\app\\frontend\\src\\schemas\\messages.ts": "31"}, {"size": 386, "mtime": 1744025730000, "results": "32", "hashOfConfig": "33"}, {"size": 4517, "mtime": 1744762374000, "results": "34", "hashOfConfig": "33"}, {"size": 8654, "mtime": 1744763048000, "results": "35", "hashOfConfig": "33"}, {"size": 710, "mtime": 1744577548000, "results": "36", "hashOfConfig": "33"}, {"size": 1684, "mtime": 1744570744000, "results": "37", "hashOfConfig": "33"}, {"size": 2467, "mtime": 1744575100000, "results": "38", "hashOfConfig": "33"}, {"size": 3679, "mtime": 1744538574000, "results": "39", "hashOfConfig": "33"}, {"size": 861, "mtime": 1735494516000, "results": "40", "hashOfConfig": "33"}, {"size": 1530, "mtime": 1744563014000, "results": "41", "hashOfConfig": "33"}, {"size": 2787, "mtime": 1744570318000, "results": "42", "hashOfConfig": "33"}, {"size": 1104, "mtime": 1744545032000, "results": "43", "hashOfConfig": "33"}, {"size": 1132, "mtime": 1744571936000, "results": "44", "hashOfConfig": "33"}, {"size": 2190, "mtime": 1744412050000, "results": "45", "hashOfConfig": "33"}, {"size": 3892, "mtime": 1744340210000, "results": "46", "hashOfConfig": "33"}, {"size": 4777, "mtime": 1744545644000, "results": "47", "hashOfConfig": "33"}, {"size": 6601, "mtime": 1742070350000, "results": "48", "hashOfConfig": "33"}, {"size": 262, "mtime": 1732192774000, "results": "49", "hashOfConfig": "33"}, {"size": 4014, "mtime": 1742069120000, "results": "50", "hashOfConfig": "33"}, {"size": 2480, "mtime": 1744574634000, "results": "51", "hashOfConfig": "33"}, {"size": 3950, "mtime": 1744552192000, "results": "52", "hashOfConfig": "33"}, {"size": 3608, "mtime": 1744343474000, "results": "53", "hashOfConfig": "33"}, {"size": 2397, "mtime": 1735488810000, "results": "54", "hashOfConfig": "33"}, {"size": 6933, "mtime": 1744762266000, "results": "55", "hashOfConfig": "33"}, {"size": 10686, "mtime": 1744340788000, "results": "56", "hashOfConfig": "33"}, {"size": 6820, "mtime": 1744552016000, "results": "57", "hashOfConfig": "33"}, {"size": 10065, "mtime": 1735496490000, "results": "58", "hashOfConfig": "33"}, {"size": 284, "mtime": 1742069110000, "results": "59", "hashOfConfig": "33"}, {"size": 819, "mtime": 1744578280000, "results": "60", "hashOfConfig": "33"}, {"size": 1319, "mtime": 1744571042000, "results": "61", "hashOfConfig": "33"}, {"size": 2006, "mtime": 1740975140000, "results": "62", "hashOfConfig": "33"}, {"size": 195, "mtime": 1744057918000, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "553f0k", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\add-application\\page.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\admin\\chats\\page.tsx", ["157", "158", "159", "160", "161", "162", "163"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\admin\\chats\\[id]\\page.tsx", ["164", "165", "166", "167", "168"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\admin\\layout.tsx", ["169", "170"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\googlecallback\\page.tsx", ["171", "172", "173", "174", "175", "176"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\layout.tsx", ["177"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\page.tsx", ["178", "179"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\auth\\forget-password\\page.tsx", ["180"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\auth\\layout.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\auth\\page.tsx", ["181", "182"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\auth\\reset-password\\page.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\app\\layout.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\authentication\\ForgetPasswordForm.tsx", ["183", "184"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\authentication\\LoginForm.tsx", ["185", "186"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\authentication\\ResetPasswordForm.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\authentication\\SignUpForm.tsx", ["187", "188"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\global\\ClientProviders.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\global\\Footer.tsx", ["189", "190"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\global\\Header.tsx", ["191", "192", "193", "194"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\AlertItemDetails\\page.tsx", ["195", "196", "197", "198"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\AlertSection\\index.tsx", ["199"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\CallUs\\index.tsx", ["200"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\Chat\\index.tsx", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\EmergencyForm\\index.tsx", ["201", "202", "203"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\ui\\animated-modal.tsx", ["204", "205"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\components\\ui\\file-upload.tsx", ["206", "207", "208", "209"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\lib\\data.ts", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\lib\\utils.ts", ["210"], [], "G:\\Graduation project 2025\\app\\frontend\\src\\schemas\\application.ts", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\schemas\\auth.ts", [], [], "G:\\Graduation project 2025\\app\\frontend\\src\\schemas\\messages.ts", [], [], {"ruleId": "211", "severity": 2, "message": "212", "line": 3, "column": 31, "nodeType": null, "messageId": "213", "endLine": 3, "endColumn": 41}, {"ruleId": "211", "severity": 2, "message": "214", "line": 4, "column": 10, "nodeType": null, "messageId": "213", "endLine": 4, "endColumn": 21}, {"ruleId": "211", "severity": 2, "message": "215", "line": 4, "column": 23, "nodeType": null, "messageId": "213", "endLine": 4, "endColumn": 35}, {"ruleId": "211", "severity": 2, "message": "216", "line": 5, "column": 38, "nodeType": null, "messageId": "213", "endLine": 5, "endColumn": 43}, {"ruleId": "211", "severity": 2, "message": "217", "line": 9, "column": 10, "nodeType": null, "messageId": "213", "endLine": 9, "endColumn": 21}, {"ruleId": "218", "severity": 1, "message": "219", "line": 48, "column": 8, "nodeType": "220", "endLine": 48, "endColumn": 10, "suggestions": "221"}, {"ruleId": "211", "severity": 2, "message": "222", "line": 80, "column": 51, "nodeType": null, "messageId": "213", "endLine": 80, "endColumn": 56}, {"ruleId": "211", "severity": 2, "message": "223", "line": 38, "column": 12, "nodeType": null, "messageId": "213", "endLine": 38, "endColumn": 20}, {"ruleId": "211", "severity": 2, "message": "224", "line": 38, "column": 22, "nodeType": null, "messageId": "213", "endLine": 38, "endColumn": 33}, {"ruleId": "225", "severity": 2, "message": "226", "line": 66, "column": 5, "nodeType": "227", "endLine": 66, "endColumn": 14}, {"ruleId": "218", "severity": 1, "message": "228", "line": 113, "column": 8, "nodeType": "220", "endLine": 113, "endColumn": 22, "suggestions": "229"}, {"ruleId": "225", "severity": 2, "message": "226", "line": 115, "column": 5, "nodeType": "227", "endLine": 115, "endColumn": 14}, {"ruleId": "211", "severity": 2, "message": "212", "line": 1, "column": 17, "nodeType": null, "messageId": "213", "endLine": 1, "endColumn": 27}, {"ruleId": "211", "severity": 2, "message": "217", "line": 2, "column": 10, "nodeType": null, "messageId": "213", "endLine": 2, "endColumn": 21}, {"ruleId": "211", "severity": 2, "message": "230", "line": 2, "column": 17, "nodeType": null, "messageId": "213", "endLine": 2, "endColumn": 20}, {"ruleId": "231", "severity": 2, "message": "232", "line": 7, "column": 14, "nodeType": "233", "messageId": "234", "endLine": 7, "endColumn": 16, "suggestions": "235"}, {"ruleId": "211", "severity": 2, "message": "236", "line": 11, "column": 12, "nodeType": null, "messageId": "213", "endLine": 11, "endColumn": 19}, {"ruleId": "225", "severity": 2, "message": "226", "line": 17, "column": 5, "nodeType": "227", "endLine": 17, "endColumn": 14}, {"ruleId": "211", "severity": 2, "message": "237", "line": 18, "column": 15, "nodeType": null, "messageId": "213", "endLine": 18, "endColumn": 18}, {"ruleId": "218", "severity": 1, "message": "238", "line": 44, "column": 8, "nodeType": "220", "endLine": 44, "endColumn": 10, "suggestions": "239"}, {"ruleId": "218", "severity": 1, "message": "240", "line": 69, "column": 8, "nodeType": "220", "endLine": 69, "endColumn": 10, "suggestions": "241"}, {"ruleId": "211", "severity": 2, "message": "242", "line": 12, "column": 36, "nodeType": null, "messageId": "213", "endLine": 12, "endColumn": 41}, {"ruleId": "211", "severity": 2, "message": "243", "line": 13, "column": 11, "nodeType": null, "messageId": "213", "endLine": 13, "endColumn": 17}, {"ruleId": "231", "severity": 2, "message": "232", "line": 4, "column": 14, "nodeType": "233", "messageId": "234", "endLine": 4, "endColumn": 16, "suggestions": "244"}, {"ruleId": "231", "severity": 2, "message": "232", "line": 12, "column": 14, "nodeType": "233", "messageId": "234", "endLine": 12, "endColumn": 16, "suggestions": "245"}, {"ruleId": "225", "severity": 2, "message": "246", "line": 15, "column": 20, "nodeType": "227", "endLine": 15, "endColumn": 29}, {"ruleId": "231", "severity": 2, "message": "232", "line": 11, "column": 14, "nodeType": "233", "messageId": "234", "endLine": 11, "endColumn": 16, "suggestions": "247"}, {"ruleId": "211", "severity": 2, "message": "248", "line": 14, "column": 11, "nodeType": null, "messageId": "213", "endLine": 14, "endColumn": 17}, {"ruleId": "211", "severity": 2, "message": "236", "line": 18, "column": 12, "nodeType": null, "messageId": "213", "endLine": 18, "endColumn": 19}, {"ruleId": "211", "severity": 2, "message": "249", "line": 18, "column": 32, "nodeType": null, "messageId": "213", "endLine": 18, "endColumn": 44}, {"ruleId": "211", "severity": 2, "message": "250", "line": 6, "column": 10, "nodeType": null, "messageId": "213", "endLine": 6, "endColumn": 27}, {"ruleId": "211", "severity": 2, "message": "236", "line": 15, "column": 12, "nodeType": null, "messageId": "213", "endLine": 15, "endColumn": 19}, {"ruleId": "251", "severity": 2, "message": "252", "line": 30, "column": 34, "nodeType": "253", "messageId": "254", "suggestions": "255"}, {"ruleId": "251", "severity": 2, "message": "252", "line": 34, "column": 29, "nodeType": "253", "messageId": "254", "suggestions": "256"}, {"ruleId": "231", "severity": 2, "message": "232", "line": 9, "column": 14, "nodeType": "233", "messageId": "234", "endLine": 9, "endColumn": 16, "suggestions": "257"}, {"ruleId": "211", "severity": 2, "message": "242", "line": 11, "column": 32, "nodeType": null, "messageId": "213", "endLine": 11, "endColumn": 37}, {"ruleId": "211", "severity": 2, "message": "258", "line": 12, "column": 12, "nodeType": null, "messageId": "213", "endLine": 12, "endColumn": 13}, {"ruleId": "211", "severity": 2, "message": "259", "line": 12, "column": 15, "nodeType": null, "messageId": "213", "endLine": 12, "endColumn": 17}, {"ruleId": "211", "severity": 2, "message": "260", "line": 6, "column": 10, "nodeType": null, "messageId": "213", "endLine": 6, "endColumn": 15}, {"ruleId": "211", "severity": 2, "message": "261", "line": 6, "column": 17, "nodeType": null, "messageId": "213", "endLine": 6, "endColumn": 23}, {"ruleId": "211", "severity": 2, "message": "262", "line": 6, "column": 25, "nodeType": null, "messageId": "213", "endLine": 6, "endColumn": 28}, {"ruleId": "211", "severity": 2, "message": "263", "line": 26, "column": 7, "nodeType": null, "messageId": "213", "endLine": 26, "endColumn": 18}, {"ruleId": "211", "severity": 2, "message": "264", "line": 6, "column": 5, "nodeType": null, "messageId": "213", "endLine": 6, "endColumn": 17}, {"ruleId": "231", "severity": 2, "message": "232", "line": 6, "column": 14, "nodeType": "233", "messageId": "234", "endLine": 6, "endColumn": 16, "suggestions": "265"}, {"ruleId": "211", "severity": 2, "message": "266", "line": 16, "column": 10, "nodeType": null, "messageId": "213", "endLine": 16, "endColumn": 17}, {"ruleId": "211", "severity": 2, "message": "267", "line": 42, "column": 12, "nodeType": null, "messageId": "213", "endLine": 42, "endColumn": 26}, {"ruleId": "211", "severity": 2, "message": "268", "line": 89, "column": 18, "nodeType": null, "messageId": "213", "endLine": 89, "endColumn": 23}, {"ruleId": "269", "severity": 2, "message": "270", "line": 224, "column": 15, "nodeType": "227", "messageId": "271", "endLine": 224, "endColumn": 23}, {"ruleId": "272", "severity": 2, "message": "273", "line": 227, "column": 34, "nodeType": "274", "messageId": "275", "endLine": 227, "endColumn": 37, "suggestions": "276"}, {"ruleId": "211", "severity": 2, "message": "277", "line": 6, "column": 10, "nodeType": null, "messageId": "213", "endLine": 6, "endColumn": 12}, {"ruleId": "211", "severity": 2, "message": "278", "line": 7, "column": 8, "nodeType": null, "messageId": "213", "endLine": 7, "endColumn": 13}, {"ruleId": "279", "severity": 2, "message": "280", "line": 41, "column": 9, "nodeType": "281", "messageId": "282", "endLine": 41, "endColumn": 40}, {"ruleId": "279", "severity": 2, "message": "280", "line": 125, "column": 49, "nodeType": "281", "messageId": "282", "endLine": 125, "endColumn": 77}, {"ruleId": "211", "severity": 2, "message": "283", "line": 8, "column": 34, "nodeType": null, "messageId": "213", "endLine": 8, "endColumn": 35}, "@typescript-eslint/no-unused-vars", "'useContext' is defined but never used.", "unusedVar", "'ChevronLeft' is defined but never used.", "'ChevronRight' is defined but never used.", "'Input' is defined but never used.", "'UserContext' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchRooms'. Either include it or remove the dependency array.", "ArrayExpression", ["284"], "'index' is defined but never used.", "'userInfo' is assigned a value but never used.", "'setUserInfo' is assigned a value but never used.", "react-hooks/rules-of-hooks", "React Hook \"useEffect\" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?", "Identifier", "React Hook useEffect has a missing dependency: 'id'. Either include it or remove the dependency array.", ["285"], "'use' is defined but never used.", "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["286", "287"], "'cookies' is assigned a value but never used.", "'res' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'code', 'router', and 'setCookie'. Either include them or remove the dependency array.", ["288"], "React Hook useEffect has a missing dependency: 'cookies'. Either include it or remove the dependency array.", ["289"], "'props' is defined but never used.", "'alerts' is assigned a value but never used.", ["290", "291"], ["292", "293"], "React Hook \"useRouter\" is called in function \"page\" that is neither a React function component nor a custom React Hook function. React component names must start with an uppercase letter. React Hook names must start with the word \"use\".", ["294", "295"], "'router' is assigned a value but never used.", "'removeCookie' is assigned a value but never used.", "'ArrowUpWideNarrow' is defined but never used.", "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["296", "297", "298", "299"], ["300", "301", "302", "303"], ["304", "305"], "'_' is assigned a value but never used.", "'__' is assigned a value but never used.", "'Popup' is defined but never used.", "'useMap' is defined but never used.", "'Map' is defined but never used.", "'middleOfUSA' is assigned a value but never used.", "'ModalContent' is defined but never used.", ["306", "307"], "'fetcher' is defined but never used.", "'selectedImages' is assigned a value but never used.", "'error' is defined but never used.", "@typescript-eslint/no-unsafe-function-type", "The `Function` type accepts any function-like value.\nPrefer explicitly defining any function parameters and return type.", "bannedFunctionType", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["308", "309"], "'on' is defined but never used.", "'Image' is defined but never used.", "@typescript-eslint/no-unused-expressions", "Expected an assignment or function call and instead saw an expression.", "ExpressionStatement", "unusedExpression", "'J' is defined but never used.", {"desc": "310", "fix": "311"}, {"desc": "312", "fix": "313"}, {"messageId": "314", "data": "315", "fix": "316", "desc": "317"}, {"messageId": "314", "data": "318", "fix": "319", "desc": "320"}, {"desc": "321", "fix": "322"}, {"desc": "323", "fix": "324"}, {"messageId": "314", "data": "325", "fix": "326", "desc": "317"}, {"messageId": "314", "data": "327", "fix": "328", "desc": "320"}, {"messageId": "314", "data": "329", "fix": "330", "desc": "317"}, {"messageId": "314", "data": "331", "fix": "332", "desc": "320"}, {"messageId": "314", "data": "333", "fix": "334", "desc": "317"}, {"messageId": "314", "data": "335", "fix": "336", "desc": "320"}, {"messageId": "337", "data": "338", "fix": "339", "desc": "340"}, {"messageId": "337", "data": "341", "fix": "342", "desc": "343"}, {"messageId": "337", "data": "344", "fix": "345", "desc": "346"}, {"messageId": "337", "data": "347", "fix": "348", "desc": "349"}, {"messageId": "337", "data": "350", "fix": "351", "desc": "340"}, {"messageId": "337", "data": "352", "fix": "353", "desc": "343"}, {"messageId": "337", "data": "354", "fix": "355", "desc": "346"}, {"messageId": "337", "data": "356", "fix": "357", "desc": "349"}, {"messageId": "314", "data": "358", "fix": "359", "desc": "317"}, {"messageId": "314", "data": "360", "fix": "361", "desc": "320"}, {"messageId": "314", "data": "362", "fix": "363", "desc": "317"}, {"messageId": "314", "data": "364", "fix": "365", "desc": "320"}, {"messageId": "366", "fix": "367", "desc": "368"}, {"messageId": "369", "fix": "370", "desc": "371"}, "Update the dependencies array to be: [fetchRooms]", {"range": "372", "text": "373"}, "Update the dependencies array to be: [access, id, room]", {"range": "374", "text": "375"}, "replaceEmptyObjectType", {"replacement": "376"}, {"range": "377", "text": "376"}, "Replace `{}` with `object`.", {"replacement": "378"}, {"range": "379", "text": "378"}, "Replace `{}` with `unknown`.", "Update the dependencies array to be: [code, router, setCookie]", {"range": "380", "text": "381"}, "Update the dependencies array to be: [cookies]", {"range": "382", "text": "383"}, {"replacement": "376"}, {"range": "384", "text": "376"}, {"replacement": "378"}, {"range": "385", "text": "378"}, {"replacement": "376"}, {"range": "386", "text": "376"}, {"replacement": "378"}, {"range": "387", "text": "378"}, {"replacement": "376"}, {"range": "388", "text": "376"}, {"replacement": "378"}, {"range": "389", "text": "378"}, "replaceWithAlt", {"alt": "390"}, {"range": "391", "text": "392"}, "Replace with `&quot;`.", {"alt": "393"}, {"range": "394", "text": "395"}, "Replace with `&ldquo;`.", {"alt": "396"}, {"range": "397", "text": "398"}, "Replace with `&#34;`.", {"alt": "399"}, {"range": "400", "text": "401"}, "Replace with `&rdquo;`.", {"alt": "390"}, {"range": "402", "text": "403"}, {"alt": "393"}, {"range": "404", "text": "405"}, {"alt": "396"}, {"range": "406", "text": "407"}, {"alt": "399"}, {"range": "408", "text": "409"}, {"replacement": "376"}, {"range": "410", "text": "376"}, {"replacement": "378"}, {"range": "411", "text": "378"}, {"replacement": "376"}, {"range": "412", "text": "376"}, {"replacement": "378"}, {"range": "413", "text": "378"}, "suggestUnknown", {"range": "414", "text": "378"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "415", "text": "416"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", [1333, 1335], "[fetchRooms]", [3524, 3538], "[access, id, room]", "object", [222, 224], "unknown", [222, 224], [1445, 1447], "[code, router, setCookie]", [2050, 2052], "[cookies]", [122, 124], [122, 124], [422, 424], [422, 424], [417, 419], [417, 419], "&quot;", [1161, 1225], "\n                            منصة &quot;\n                            ", "&ldquo;", [1161, 1225], "\n                            منصة &ldquo;\n                            ", "&#34;", [1161, 1225], "\n                            منصة &#34;\n                            ", "&rdquo;", [1161, 1225], "\n                            منصة &rdquo;\n                            ", [1349, 1649], "\n                            &quot; هي منصة تواصل للطوارئ تتيح للمستخدمين في فلسطين\n                            إرسال واستقبال الإشعارات الطوارئ، سواء كانت تتعلق\n                            بمساعدة طبية أو أمن طلب مساعدة طبية أو الإبلاغ عن\n                            مخاطر تهددهم\n                        ", [1349, 1649], "\n                            &ldquo; هي منصة تواصل للطوارئ تتيح للمستخدمين في فلسطين\n                            إرسال واستقبال الإشعارات الطوارئ، سواء كانت تتعلق\n                            بمساعدة طبية أو أمن طلب مساعدة طبية أو الإبلاغ عن\n                            مخاطر تهددهم\n                        ", [1349, 1649], "\n                            &#34; هي منصة تواصل للطوارئ تتيح للمستخدمين في فلسطين\n                            إرسال واستقبال الإشعارات الطوارئ، سواء كانت تتعلق\n                            بمساعدة طبية أو أمن طلب مساعدة طبية أو الإبلاغ عن\n                            مخاطر تهددهم\n                        ", [1349, 1649], "\n                            &rdquo; هي منصة تواصل للطوارئ تتيح للمستخدمين في فلسطين\n                            إرسال واستقبال الإشعارات الطوارئ، سواء كانت تتعلق\n                            بمساعدة طبية أو أمن طلب مساعدة طبية أو الإبلاغ عن\n                            مخاطر تهددهم\n                        ", [280, 282], [280, 282], [159, 161], [159, 161], [6255, 6258], [6255, 6258], "never"]