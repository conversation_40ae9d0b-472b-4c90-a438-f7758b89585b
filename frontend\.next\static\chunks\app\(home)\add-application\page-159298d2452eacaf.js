(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[302],{13028:(e,a,t)=>{"use strict";t.d(a,{EmergencyForm:()=>A});var l=t(95155),s=t(62177),r=t(90221),i=t(83662),n=t(12115),c=t(6425),o=t(74409),d=t(29270),m=t(66146),x=t(81495),u=t(71153);let p=["image/jpeg","image/png","image/webp","image/gif"],h=u.Ik({location:u.Yj({required_error:"يرجى تحديد موقعك"}),emergency_type:u.k5(["O","M","D"],{message:"يرجى تحديد نوع المساعدة الصحيح"}),images:u.YO(u.Nl(File)).optional().refine(e=>!e||e.every(e=>e.size<=5242880&&p.includes(e.type)),"يجب أن يكون حجم كل صورة أقل من 5 ميجابايت").refine(e=>!!e&&e.length<=5&&e.length>0,"يجب أن تحتوي الصور على صورة واحدة على الأقل ولا تزيد عن 5 صور"),description:u.Yj().min(10,"يجب أن يحتوي الوصف على 10 أحرف على الأقل")});var f=t(36545),g=t(46710),y=t(16923),j=t(58096);let v={initial:{x:0,y:0},animate:{x:20,y:-20,opacity:.9}},b={initial:{opacity:0},animate:{opacity:1}},w=e=>{let{onChange:a,isMultiple:t}=e,[s,r]=(0,n.useState)([]),i=(0,n.useRef)(null),c=e=>{r(a=>[...a,...e]),a&&a(e)},{getRootProps:o,isDragActive:d}=(0,g.VB)({multiple:!1,noClick:!0,onDrop:c,onDropRejected:e=>{console.log(e)}});return(0,l.jsx)("div",{className:"w-full",...o(),children:(0,l.jsxs)(f.P.div,{onClick:()=>{var e;null==(e=i.current)||e.click()},whileHover:"animate",className:"p-10 group/file block rounded-lg cursor-pointer w-full relative overflow-hidden",children:[(0,l.jsx)("input",{ref:i,id:"file-upload-handle",type:"file",multiple:t,onChange:e=>c(Array.from(e.target.files||[])),className:"hidden"}),(0,l.jsx)("div",{className:"absolute inset-0 [mask-image:radial-gradient(ellipse_at_center,white,transparent)]",children:(0,l.jsx)(N,{})}),(0,l.jsx)("div",{className:"flex flex-col items-center justify-center",children:(0,l.jsxs)("div",{className:"relative w-full mt-10 max-w-xl mx-auto",children:[s.length>0&&s.map((e,t)=>(0,l.jsxs)(f.P.div,{layoutId:0===t?"file-upload":"file-upload-"+t,className:(0,j.cn)("relative z-40 bg-white dark:bg-neutral-900 flex flex-col items-start justify-start md:h-24 p-4 mt-4 w-full mx-auto rounded-md","shadow-sm"),children:[(0,l.jsxs)("div",{className:"flex justify-between w-full items-center gap-4",children:[(0,l.jsx)(f.P.p,{initial:{opacity:0},animate:{opacity:1},layout:!0,className:"text-base text-neutral-700 dark:text-neutral-300 truncate max-w-xs",children:e.name}),(0,l.jsxs)(f.P.p,{initial:{opacity:0},animate:{opacity:1},layout:!0,className:"rounded-lg px-2 py-1 w-fit flex-shrink-0 text-sm text-neutral-600 dark:bg-neutral-800 dark:text-white shadow-input",children:[(e.size/1048576).toFixed(2)," ","MB"]}),(0,l.jsx)(m.T,{size:"sm",variant:"light",color:"danger",className:"min-w-2 font-bold text-lg absolute -top-3 -right-3",onClick:()=>{r(e=>e.filter((e,a)=>a!==t)),a&&a(s)},children:"X"})]}),(0,l.jsxs)("div",{className:"flex text-sm md:flex-row flex-col items-start md:items-center w-full mt-2 justify-between text-neutral-600 dark:text-neutral-400",children:[(0,l.jsx)(f.P.p,{initial:{opacity:0},animate:{opacity:1},layout:!0,className:"px-1 py-0.5 rounded-md bg-gray-100 dark:bg-neutral-800 ",children:e.type}),(0,l.jsxs)(f.P.p,{initial:{opacity:0},animate:{opacity:1},layout:!0,children:["modified"," ",new Date(e.lastModified).toLocaleDateString()]})]})]},"file"+t)),!s.length&&(0,l.jsx)(f.P.div,{layoutId:"file-upload",variants:v,transition:{type:"spring",stiffness:300,damping:20},className:(0,j.cn)("relative group-hover/file:shadow-2xl z-40 bg-white dark:bg-neutral-900 flex items-center justify-center h-32 mt-4 w-full max-w-[8rem] mx-auto rounded-md","shadow-[0px_10px_50px_rgba(0,0,0,0.1)]"),children:d?(0,l.jsxs)(f.P.p,{initial:{opacity:0},animate:{opacity:1},className:"text-neutral-600 flex flex-col items-center",children:["Drop it",(0,l.jsx)(y.A,{className:"h-4 w-4 text-neutral-600 dark:text-neutral-400"})]}):(0,l.jsx)(y.A,{className:"h-4 w-4 text-neutral-600 dark:text-neutral-300"})}),!s.length&&(0,l.jsx)(f.P.div,{variants:b,className:"absolute opacity-0 border border-dashed border-sky-400 inset-0 z-30 bg-transparent flex items-center justify-center h-32 mt-4 w-full max-w-[8rem] mx-auto rounded-md"})]})})]})})};function N(){return(0,l.jsx)("div",{className:"flex bg-gray-100 dark:bg-neutral-900 flex-shrink-0 flex-wrap justify-center items-center gap-x-px gap-y-px  scale-105",children:Array.from({length:11}).map((e,a)=>Array.from({length:41}).map((e,t)=>{let s=41*a+t;return(0,l.jsx)("div",{className:"w-10 h-10 flex flex-shrink-0 rounded-[2px] ".concat(s%2==0?"bg-gray-50 dark:bg-neutral-950":"bg-gray-50 dark:bg-neutral-950 shadow-[0px_0px_1px_3px_rgba(255,255,255,1)_inset] dark:shadow-[0px_0px_1px_3px_rgba(0,0,0,1)_inset]")},"".concat(t,"-").concat(a))}))})}var k=t(56671),_=t(35695);let P=["القدس","رام الله","بيت لحم","الخليل","نابلس","جنين","طولكرم","قلقيلية","أريحا"],I=[{value:"M",text:"طبية"},{value:"O",text:"إغاثة"},{value:"D",text:"خطر"}];function A(e){let{token:a}=e,t=(0,_.useRouter)(),[u,p]=(0,n.useState)([]),{control:f,handleSubmit:g,setValue:y,formState:{errors:j}}=(0,s.mN)({resolver:(0,r.u)(h),defaultValues:{location:"",description:"",images:[]}});async function v(e){try{var l;let s=new FormData;null==(l=e.images)||l.forEach(e=>{s.append("images",e)}),s.append("location",e.location),s.append("emergency_type",e.emergency_type),s.append("description",e.description);let r=await fetch("".concat("http://localhost:8000","/emergency/create/"),{method:"POST",body:s,headers:{Authorization:"Bearer ".concat(a)}});if(201===r.status)k.o.success("تم إرسال الطلب بنجاح"),t.refresh(),t.push("/");else{let e=await r.json();console.log(e),k.o.error("حدث خطأ أثناء إرسال الطلب, برجاء اعادة المحاولة")}}catch(e){k.o.error("حدث خطأ أثناء إرسال الطلب, برجاء اعادة المحاولة")}}return(0,l.jsxs)("div",{className:"w-full max-w-2xl mx-auto p-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold",children:"أرسل إشعار للطوارئ"}),(0,l.jsx)(i.A,{className:"w-6 h-6 text-blue-500"})]}),(0,l.jsxs)("form",{onSubmit:g(v),className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"حدد موقعك (إجباري) *"}),(0,l.jsx)(s.xI,{name:"location",control:f,render:e=>{var a;let{field:t}=e;return(0,l.jsx)(c.d,{label:"من فضلك اختر موقعك",...t,errorMessage:null==(a=j.location)?void 0:a.message,isInvalid:!!j.location,children:P.map(e=>(0,l.jsx)(o.y,{value:e,children:e},e))})}})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"نوع المساعدة (إجباري) *"}),(0,l.jsx)(s.xI,{name:"emergency_type",control:f,render:e=>{var a;let{field:t}=e;return(0,l.jsx)(c.d,{label:"من فضلك اختر نوع المساعدة",...t,errorMessage:null==(a=j.emergency_type)?void 0:a.message,isInvalid:!!j.emergency_type,children:I.map(e=>(0,l.jsx)(o.y,{value:e.value,children:e.text},e.value))})}})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"ارفق صور للحالة (إجباري) *"}),(0,l.jsx)(w,{isMultiple:!0,onChange:e=>{if(!e||e.length<=0)return;let a=Array.from(e).filter(e=>e.type.startsWith("image/"));p(e=>[...e,...a]),y("images",a)}}),j.images&&(0,l.jsx)("p",{className:"text-xs text-danger",children:j.images.message})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"وصف الحالة (إجباري) *"}),(0,l.jsx)(s.xI,{name:"description",control:f,render:e=>{var a;let{field:t}=e;return(0,l.jsx)(d.P,{placeholder:"من فضلك اكتب وصف الحالة",minRows:25,...t,errorMessage:null==(a=j.description)?void 0:a.message,isInvalid:!!j.description})}})]}),(0,l.jsxs)("div",{className:"flex justify-between items-center gap-2",children:[(0,l.jsx)(m.T,{as:x.h,href:"/",type:"button",variant:"bordered",color:"default",children:"إلغاء"}),(0,l.jsx)(m.T,{type:"submit",color:"primary",children:"أرسل الطلب"})]})]})]})}},87384:(e,a,t)=>{Promise.resolve().then(t.bind(t,13028))}},e=>{var a=a=>e(e.s=a);e.O(0,[477,146,688,671,833,943,168,161,674,441,684,358],()=>a(87384)),_N_E=e.O()}]);