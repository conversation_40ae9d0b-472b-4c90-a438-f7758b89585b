"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[477],{22989:(e,t,n)=>{n.d(t,{Fe:()=>s,_h:()=>f,pg:()=>l,rd:()=>u,sU:()=>c});var r=n(47024),o=n(66680),i=n(12115);let a=(0,i.createContext)({isNative:!0,open:function(e,t){d(e,e=>s(e,t))},useHref:e=>e});function l(e){let{children:t,navigate:n,useHref:r}=e,o=(0,i.useMemo)(()=>({isNative:!1,open:(e,t,r,o)=>{d(e,e=>{c(e,t)?n(r,o):s(e,t)})},useHref:r||(e=>e)}),[n,r]);return i.createElement(a.Provider,{value:o},t)}function u(){return(0,i.useContext)(a)}function c(e,t){let n=e.getAttribute("target");return(!n||"_self"===n)&&e.origin===location.origin&&!e.hasAttribute("download")&&!t.metaKey&&!t.ctrlKey&&!t.altKey&&!t.shiftKey}function s(e,t,n=!0){var i,a;let{metaKey:l,ctrlKey:u,altKey:c,shiftKey:d}=t;(0,o.gm)()&&(null==(a=window.event)||null==(i=a.type)?void 0:i.startsWith("key"))&&"_blank"===e.target&&((0,o.cX)()?l=!0:u=!0);let f=(0,o.Tc)()&&(0,o.cX)()&&!(0,o.bh)()&&1?new KeyboardEvent("keydown",{keyIdentifier:"Enter",metaKey:l,ctrlKey:u,altKey:c,shiftKey:d}):new MouseEvent("click",{metaKey:l,ctrlKey:u,altKey:c,shiftKey:d,bubbles:!0,cancelable:!0});s.isOpening=n,(0,r.e)(e),e.dispatchEvent(f),s.isOpening=!1}function d(e,t){if(e instanceof HTMLAnchorElement)t(e);else if(e.hasAttribute("data-href")){let n=document.createElement("a");n.href=e.getAttribute("data-href"),e.hasAttribute("data-target")&&(n.target=e.getAttribute("data-target")),e.hasAttribute("data-rel")&&(n.rel=e.getAttribute("data-rel")),e.hasAttribute("data-download")&&(n.download=e.getAttribute("data-download")),e.hasAttribute("data-ping")&&(n.ping=e.getAttribute("data-ping")),e.hasAttribute("data-referrer-policy")&&(n.referrerPolicy=e.getAttribute("data-referrer-policy")),e.appendChild(n),t(n),e.removeChild(n)}}function f(e){var t;let n=u().useHref(null!=(t=null==e?void 0:e.href)?t:"");return{href:(null==e?void 0:e.href)?n:void 0,target:null==e?void 0:e.target,rel:null==e?void 0:e.rel,download:null==e?void 0:e.download,ping:null==e?void 0:e.ping,referrerPolicy:null==e?void 0:e.referrerPolicy}}s.isOpening=!1},42810:(e,t,n)=>{n.d(t,{q:()=>o});var r=n(12115);function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{strict:t=!0,errorMessage:n="useContext: `context` is undefined. Seems you forgot to wrap component within the Provider",name:o}=e,i=r.createContext(void 0);return i.displayName=o,[i.Provider,function e(){var o;let a=r.useContext(i);if(!a&&t){let t=Error(n);throw t.name="ContextError",null==(o=Error.captureStackTrace)||o.call(Error,t,e),t}return a},i]}},44823:(e,t,n)=>{n.d(t,{Cc:()=>s,wR:()=>p});var r=n(12115);let o={prefix:String(Math.round(1e10*Math.random())),current:0},i=r.createContext(o),a=r.createContext(!1),l=!!("undefined"!=typeof window&&window.document&&window.document.createElement),u=new WeakMap;function c(e=!1){let t=(0,r.useContext)(i),n=(0,r.useRef)(null);if(null===n.current&&!e){var o,a;let e=null==(a=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)||null==(o=a.ReactCurrentOwner)?void 0:o.current;if(e){let n=u.get(e);null==n?u.set(e,{id:t.current,state:e.memoizedState}):e.memoizedState!==n.state&&(t.current=n.id,u.delete(e))}n.current=++t.current}return n.current}let s="function"==typeof r.useId?function(e){let t=r.useId(),[n]=(0,r.useState)(p()),i=n?"react-aria":`react-aria${o.prefix}`;return e||`${i}-${t}`}:function(e){let t=(0,r.useContext)(i);t!==o||l||console.warn("When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.");let n=c(!!e),a=`react-aria${t.prefix}`;return e||`${a}-${n}`};function d(){return!1}function f(){return!0}function g(e){return()=>{}}function p(){return"function"==typeof r.useSyncExternalStore?r.useSyncExternalStore(g,d,f):(0,r.useContext)(a)}},47024:(e,t,n)=>{function r(e){if(function(){if(null==o){o=!1;try{document.createElement("div").focus({get preventScroll(){return o=!0,!0}})}catch{}}return o}())e.focus({preventScroll:!0});else{let t=function(e){let t=e.parentNode,n=[],r=document.scrollingElement||document.documentElement;for(;t instanceof HTMLElement&&t!==r;)(t.offsetHeight<t.scrollHeight||t.offsetWidth<t.scrollWidth)&&n.push({element:t,scrollTop:t.scrollTop,scrollLeft:t.scrollLeft}),t=t.parentNode;return r instanceof HTMLElement&&n.push({element:r,scrollTop:r.scrollTop,scrollLeft:r.scrollLeft}),n}(e);e.focus(),function(e){for(let{element:t,scrollTop:n,scrollLeft:r}of e)t.scrollTop=n,t.scrollLeft=r}(t)}}n.d(t,{e:()=>r});let o=null},51508:(e,t,n)=>{n.d(t,{Q:()=>r});let r=(0,n(12115).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},66680:(e,t,n)=>{function r(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&((null==(t=window.navigator.userAgentData)?void 0:t.brands.some(t=>e.test(t.brand)))||e.test(window.navigator.userAgent))}function o(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&e.test((null==(t=window.navigator.userAgentData)?void 0:t.platform)||window.navigator.platform)}function i(e){let t=null;return()=>(null==t&&(t=e()),t)}n.d(t,{Tc:()=>d,bh:()=>u,cX:()=>a,gm:()=>p,lg:()=>s,m0:()=>g,un:()=>c});let a=i(function(){return o(/^Mac/i)}),l=i(function(){return o(/^iPhone/i)}),u=i(function(){return o(/^iPad/i)||a()&&navigator.maxTouchPoints>1}),c=i(function(){return l()||u()}),s=i(function(){return a()||c()}),d=i(function(){return r(/AppleWebKit/i)&&!f()}),f=i(function(){return r(/Chrome/i)}),g=i(function(){return r(/Android/i)}),p=i(function(){return r(/Firefox/i)})},74492:(e,t,n)=>{n.d(t,{W:()=>r});let r={skipAnimations:!1,useManualTiming:!1}},75894:(e,t,n)=>{n.d(t,{n:()=>r,o:()=>o});var[r,o]=(0,n(42810).q)({name:"ProviderContext",strict:!1})},82885:(e,t,n)=>{n.d(t,{M:()=>o});var r=n(12115);function o(e){let t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},95500:(e,t,n)=>{n.d(t,{J:()=>l,D:()=>a});let r=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function o(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||r.has(e)}let i=e=>!o(e);function a(e){e&&(i=t=>t.startsWith("on")?!o(t):e(t))}try{a(require("@emotion/is-prop-valid").default)}catch(e){}function l(e,t,n){let r={};for(let a in e)("values"!==a||"object"!=typeof e.values)&&(i(a)||!0===n&&o(a)||!t&&!o(a)||e.draggable&&a.startsWith("onDrag"))&&(r[a]=e[a]);return r}}}]);