(()=>{var e={};e.id=813,e.ids=[813],e.modules={2326:(e,t,r)=>{Promise.resolve().then(r.bind(r,11075)),Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,72873)),Promise.resolve().then(r.bind(r,23650))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,t,r)=>{let{createProxy:o}=r(39844);e.exports=o("G:\\Graduation project 2025\\app\\frontend\\node_modules\\next\\dist\\client\\app-dir\\link.js")},8710:(e,t,r)=>{"use strict";r.d(t,{f:()=>a,u:()=>o});var[o,a]=(0,r(40572).q)({name:"CardContext",strict:!0,errorMessage:"useCardContext: `context` is undefined. Seems you forgot to wrap component within <Card />"})},10529:(e,t,r)=>{"use strict";r.d(t,{Button:()=>o.T});var o=r(36424)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,t,r)=>{"use strict";async function o(e,t,r="GET",a){return await fetch("http://localhost:8000"+e,{method:r,body:null===t?null:JSON.stringify(t),headers:{"Content-Type":"application/json",Authorization:a?`Bearer ${a}`:""}})}r.d(t,{G:()=>o})},11075:(e,t,r)=>{"use strict";r.d(t,{Button:()=>a});var o=r(12907);let a=(0,o.registerClientReference)(function(){throw Error("Attempted to call Button() from the server but Button is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app\\frontend\\node_modules\\@nextui-org\\button\\dist\\index.mjs","Button");(0,o.registerClientReference)(function(){throw Error("Attempted to call ButtonGroup() from the server but ButtonGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app\\frontend\\node_modules\\@nextui-org\\button\\dist\\index.mjs","ButtonGroup"),(0,o.registerClientReference)(function(){throw Error("Attempted to call ButtonGroupProvider() from the server but ButtonGroupProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app\\frontend\\node_modules\\@nextui-org\\button\\dist\\index.mjs","ButtonGroupProvider"),(0,o.registerClientReference)(function(){throw Error("Attempted to call useButton() from the server but useButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app\\frontend\\node_modules\\@nextui-org\\button\\dist\\index.mjs","useButton"),(0,o.registerClientReference)(function(){throw Error("Attempted to call useButtonGroup() from the server but useButtonGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app\\frontend\\node_modules\\@nextui-org\\button\\dist\\index.mjs","useButtonGroup"),(0,o.registerClientReference)(function(){throw Error("Attempted to call useButtonGroupContext() from the server but useButtonGroupContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app\\frontend\\node_modules\\@nextui-org\\button\\dist\\index.mjs","useButtonGroupContext")},11468:(e,t,r)=>{"use strict";r.d(t,{P:()=>u});var o=r(34084),a=r(31548),s=r(64586),n=r(3388),l=r(31208),i=r(47383),d=r(72709);let c=(0,i.C)({...a.W,...n.n,...s.$,...l.Z},d.J),u=(0,o.I)(c)},16784:(e,t,r)=>{"use strict";r.d(t,{default:()=>c});var o=r(60687),a=r(63257),s=r(86760),n=r(82614);let l=(0,n.A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),i=(0,n.A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);var d=r(26398);function c({}){return(0,o.jsx)("section",{className:"py-12 bg-white",id:"call-us",children:(0,o.jsxs)("div",{className:"container mx-auto px-4",children:[(0,o.jsx)("h2",{className:"text-2xl font-bold text-center mb-8",children:"اتصل بنا"}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,o.jsx)(a.Z,{children:(0,o.jsxs)(s.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,o.jsx)(l,{className:"w-6 h-6 text-blue-500"}),(0,o.jsx)("h3",{className:"font-bold",children:"الهاتف"}),(0,o.jsxs)("div",{className:"space-y-1 text-sm text-default-500",children:[(0,o.jsx)("p",{dir:"ltr",children:"+970 2384501 / +970 2384501"}),(0,o.jsx)("p",{dir:"ltr",children:"+9702384501 / +970 2384501"})]})]})}),(0,o.jsx)(a.Z,{children:(0,o.jsxs)(s.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,o.jsx)(i,{className:"w-6 h-6 text-blue-500"}),(0,o.jsx)("h3",{className:"font-bold",children:"البريد الكتروني"}),(0,o.jsxs)("div",{className:"space-y-1 text-sm text-default-500",children:[(0,o.jsx)("p",{children:"AssistanceFormat.Com.Eg"}),(0,o.jsx)("p",{children:"AssistanceFormat.Com.Eg"})]})]})}),(0,o.jsx)(a.Z,{children:(0,o.jsxs)(s.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,o.jsx)(d.A,{className:"w-6 h-6 text-blue-500"}),(0,o.jsx)("h3",{className:"font-bold",children:"العنوان الرئيسي"}),(0,o.jsxs)("p",{className:"text-sm text-default-500",children:["القدس - شارع مدينة",(0,o.jsx)("br",{}),"العربية - فلسطين"]})]})})]})]})})}r(43210)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23650:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\CallUs\\index.tsx","default")},26398:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(82614).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31971:(e,t,r)=>{"use strict";r.d(t,{AlertSection:()=>I});var o=r(60687),a=r(4780),s=r(88920),n=r(11468),l=r(43210);let i=(0,l.createContext)(void 0),d=({children:e})=>{let[t,r]=(0,l.useState)(!1);return(0,o.jsx)(i.Provider,{value:{open:t,setOpen:r},children:e})},c=()=>{let e=(0,l.useContext)(i);if(!e)throw Error("useModal must be used within a ModalProvider");return e};function u({children:e}){return(0,o.jsx)(d,{children:e})}let p=({children:e,className:t})=>{let{setOpen:r}=c();return(0,o.jsx)("button",{className:(0,a.cn)("px-4 py-2 rounded-md text-black dark:text-white text-center relative overflow-hidden",t),onClick:()=>r(!0),children:e})},m=({children:e,className:t})=>{let{open:r}=c();(0,l.useEffect)(()=>{r?document.body.style.overflow="hidden":document.body.style.overflow="auto"},[r]);let i=(0,l.useRef)(null),{setOpen:d}=c();return x(i,()=>d(!1)),(0,o.jsx)(s.N,{children:r&&(0,o.jsxs)(n.P.div,{initial:{opacity:0},animate:{opacity:1,backdropFilter:"blur(10px)"},exit:{opacity:0,backdropFilter:"blur(0px)"},className:"fixed [perspective:800px] [transform-style:preserve-3d] inset-0 h-full w-full min-w-[25rem] flex items-center justify-center z-[99999]",children:[(0,o.jsx)(h,{}),(0,o.jsxs)(n.P.div,{ref:i,className:(0,a.cn)("min-h-[50%] max-h-[90%] md:max-w-[40%] bg-white dark:bg-neutral-950 border border-transparent dark:border-neutral-800 md:rounded-2xl relative z-50 flex flex-col flex-1 overflow-hidden",t),initial:{opacity:0,scale:.5,rotateX:40,y:40},animate:{opacity:1,scale:1,rotateX:0,y:0},exit:{opacity:0,scale:.8,rotateX:10},transition:{type:"spring",stiffness:260,damping:15},children:[(0,o.jsx)(f,{}),e]})]})})},h=({className:e})=>(0,o.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1,backdropFilter:"blur(10px)"},exit:{opacity:0,backdropFilter:"blur(0px)"},className:`fixed inset-0 h-full w-full bg-black bg-opacity-50 z-50 ${e}`}),f=()=>{let{setOpen:e}=c();return(0,o.jsx)("button",{onClick:()=>e(!1),className:"absolute top-4 right-4 group",children:(0,o.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-black dark:text-white h-4 w-4 group-hover:scale-125 group-hover:rotate-3 transition duration-200",children:[(0,o.jsx)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,o.jsx)("path",{d:"M18 6l-12 12"}),(0,o.jsx)("path",{d:"M6 6l12 12"})]})})},x=(e,t)=>{(0,l.useEffect)(()=>{let r=r=>{!e.current||e.current.contains(r.target)||t(r)};return document.addEventListener("mousedown",r),document.addEventListener("touchstart",r),()=>{document.removeEventListener("mousedown",r),document.removeEventListener("touchstart",r)}},[e,t])};var b=r(79293),v=r(63257),g=r(86760),w=r(82614);let j=(0,w.A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),y=(0,w.A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var k=r(85814),N=r.n(k),C=r(55150),G=r(26109),_=(0,r(72926).tv)({slots:{wrapper:"relative shadow-black/5",zoomedWrapper:"relative overflow-hidden rounded-inherit",img:"relative z-10 opacity-0 shadow-black/5 data-[loaded=true]:opacity-100",blurredImg:["absolute","z-0","inset-0","w-full","h-full","object-cover","filter","blur-lg","scale-105","saturate-150","opacity-30","translate-y-1"]},variants:{radius:{none:{},sm:{},md:{},lg:{},full:{}},shadow:{none:{wrapper:"shadow-none",img:"shadow-none"},sm:{wrapper:"shadow-small",img:"shadow-small"},md:{wrapper:"shadow-medium",img:"shadow-medium"},lg:{wrapper:"shadow-large",img:"shadow-large"}},isZoomed:{true:{img:["object-cover","transform","hover:scale-125"]}},showSkeleton:{true:{wrapper:["group","relative","overflow-hidden","bg-content3 dark:bg-content2"],img:"opacity-0"}},disableAnimation:{true:{img:"transition-none"},false:{img:"transition-transform-opacity motion-reduce:transition-none !duration-300"}}},defaultVariants:{radius:"lg",shadow:"none",isZoomed:!1,isBlurred:!1,showSkeleton:!1},compoundVariants:[{showSkeleton:!0,disableAnimation:!1,class:{wrapper:["before:opacity-100","before:absolute","before:inset-0","before:-translate-x-full","before:animate-[shimmer_2s_infinite]","before:border-t","before:border-content4/30","before:bg-gradient-to-r","before:from-transparent","before:via-content4","dark:before:via-default-700/10","before:to-transparent","after:opacity-100","after:absolute","after:inset-0","after:-z-10","after:bg-content3","dark:after:bg-content2"]}}],compoundSlots:[{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"none",class:"rounded-none"},{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"full",class:"rounded-full"},{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"sm",class:"rounded-small"},{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"md",class:"rounded-md"},{slots:["wrapper","img","blurredImg","zoomedWrapper"],radius:"lg",class:"rounded-large"}]}),E=r(54514),P=r(82432),A=r(16060),z=r(1172),S=r(78607),B=(0,G.Rf)((e,t)=>{let{Component:r,domRef:a,slots:s,classNames:n,isBlurred:i,isZoomed:d,fallbackSrc:c,removeWrapper:u,disableSkeleton:p,getImgProps:m,getWrapperProps:h,getBlurredImgProps:f}=function(e){var t,r;let o=(0,C.o)(),[a,s]=(0,G.rE)(e,_.variantKeys),{ref:n,as:i,src:d,className:c,classNames:u,loading:p,isBlurred:m,fallbackSrc:h,isLoading:f,disableSkeleton:x=!!h,removeWrapper:b=!1,onError:v,onLoad:g,srcSet:w,sizes:j,crossOrigin:y,...k}=a,N=function(e={}){let{onLoad:t,onError:r,ignoreFallback:o}=e,a=l.useSyncExternalStore(()=>()=>{},()=>!0,()=>!1),s=(0,l.useRef)(a?new Image:null),[n,i]=(0,l.useState)("pending");(0,l.useEffect)(()=>{s.current&&(s.current.onload=e=>{d(),i("loaded"),null==t||t(e)},s.current.onerror=e=>{d(),i("failed"),null==r||r(e)})},[s.current]);let d=()=>{s.current&&(s.current.onload=null,s.current.onerror=null,s.current=null)};return(0,S.U)(()=>{a&&i(function(e,t){let{loading:r,src:o,srcSet:a,crossOrigin:s,sizes:n,ignoreFallback:l}=e;if(!o)return"pending";if(l)return"loaded";let i=new Image;return(i.src=o,s&&(i.crossOrigin=s),a&&(i.srcset=a),n&&(i.sizes=n),r&&(i.loading=r),t.current=i,i.complete&&i.naturalWidth)?"loaded":"loading"}(e,s))},[a]),o?"loaded":n}({src:d,loading:p,onError:v,onLoad:g,ignoreFallback:!1,srcSet:w,sizes:j,crossOrigin:y}),B=null!=(r=null!=(t=e.disableAnimation)?t:null==o?void 0:o.disableAnimation)&&r,M="loaded"===N&&!f,I="loading"===N||f,$=e.isZoomed,R=(0,E.zD)(n),{w:W,h:L}=(0,l.useMemo)(()=>({w:a.width?"number"==typeof a.width?`${a.width}px`:a.width:"fit-content",h:a.height?"number"==typeof a.height?`${a.height}px`:a.height:"auto"}),[null==a?void 0:a.width,null==a?void 0:a.height]),D=(!d||!M)&&!!h,H=I&&!x,q=(0,l.useMemo)(()=>_({...s,disableAnimation:B,showSkeleton:H}),[(0,P.t6)(s),B,H]),F=(0,A.$)(c,null==u?void 0:u.img),U=(0,l.useCallback)(()=>{let e=D?{backgroundImage:`url(${h})`}:{};return{className:q.wrapper({class:null==u?void 0:u.wrapper}),style:{...e,maxWidth:W}}},[q,D,h,null==u?void 0:u.wrapper,W]),Z=(0,l.useCallback)(()=>({src:d,"aria-hidden":(0,z.sE)(!0),className:q.blurredImg({class:null==u?void 0:u.blurredImg})}),[q,d,null==u?void 0:u.blurredImg]);return{Component:i||"img",domRef:R,slots:q,classNames:u,isBlurred:m,disableSkeleton:x,fallbackSrc:h,removeWrapper:b,isZoomed:$,isLoading:I,getImgProps:(e={})=>{let t=(0,A.$)(F,null==e?void 0:e.className);return{src:d,ref:R,"data-loaded":(0,z.sE)(M),className:q.img({class:t}),loading:p,srcSet:w,sizes:j,crossOrigin:y,...k,style:{...(null==k?void 0:k.height)&&{height:L},...e.style,...k.style}}},getWrapperProps:U,getBlurredImgProps:Z}}({...e,ref:t}),x=(0,o.jsx)(r,{ref:a,...m()});if(u)return x;let b=(0,o.jsx)("div",{className:s.zoomedWrapper({class:null==n?void 0:n.zoomedWrapper}),children:x});return i?(0,o.jsxs)("div",{...h(),children:[d?b:x,(0,l.cloneElement)(x,f())]}):d||!p||c?(0,o.jsxs)("div",{...h(),children:[" ",d?b:x]}):x});function M({id:e}){let[t,r]=(0,l.useState)(!0),[a,s]=(0,l.useState)(null);return console.log(a),(0,o.jsx)("div",{className:"container mx-auto px-4 py-6 space-y-6",dir:"rtl",children:t?(0,o.jsx)("div",{className:"flex items-center justify-center min-h-[20rem]",children:(0,o.jsx)("p",{className:"font-bold text-xl",children:"جارى التحميل..."})}):a?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("h1",{className:"text-xl font-bold text-right mt-4",children:a.location}),(0,o.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed text-right",children:a.description}),(0,o.jsxs)("div",{className:"text-right",children:[(0,o.jsx)("h2",{className:"text-sm font-semibold mb-3",children:"الصور المرفقة"}),(0,o.jsx)("div",{className:"grid grid-cols-3 gap-4 overflow-x-auto pb-4",dir:"ltr",children:a.images.map(({id:e,image:t})=>(0,o.jsx)(B,{src:t||"/placeholder.svg",alt:`صورة ${e+1}`,className:"w-[240px] h-[240px] object-cover rounded-lg"},e))})]})]}):(0,o.jsx)("p",{children:"حدث خطأ أثناء تحميل الطلب برجاء اعادة المحاولة لاحقا"})})}function I({data:e,heading:t}){return(0,o.jsxs)("div",{className:"w-full max-w-[1200px] mx-auto px-4 py-6",children:[(0,o.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)(N(),{href:"/previous",className:"text-blue-500 hover:text-blue-600",children:(0,o.jsx)(j,{className:"w-5 h-5"})}),(0,o.jsx)("h2",{className:"text-xl font-bold",children:t})]})}),0===e.length?(0,o.jsx)("div",{className:"flex items-center justify-center w-full h-32",children:(0,o.jsx)("p",{className:"text-gray-500",children:"لا توجد تنبيهات حالياً"})}):(0,o.jsx)(b.H,{orientation:"horizontal",className:"flex gap-4 w-full overflow-x-auto pb-4",children:e.map(e=>(0,o.jsx)(v.Z,{className:"flex-none w-[300px] border border-gray-200",children:(0,o.jsxs)(g.U,{className:"gap-4",children:[(0,o.jsxs)("div",{className:"flex gap-2",children:[(0,o.jsxs)("div",{className:"space-y-1",children:[(0,o.jsx)("h3",{className:"font-semibold text-sm text-start",children:e.user_first_name+" "+e.user_last_name}),(0,o.jsx)("p",{className:"text-xs text-gray-500",children:e.location})]}),(0,o.jsx)(y,{className:"w-5 h-5 text-blue-500 mt-1 flex-shrink-0"})]}),(0,o.jsx)("div",{className:"flex justify-end gap-2 border-t-1 pt-4",children:(0,o.jsxs)(u,{children:[(0,o.jsx)(p,{className:"bg-blue-600 text-sm text-white hover:opacity-75 transition",children:"عرض التفاصيل"}),(0,o.jsx)(m,{children:(0,o.jsx)(M,{id:e.id})})]},Math.random())})]})},e.id))})]})}B.displayName="NextUI.Image"},33873:e=>{"use strict";e.exports=require("path")},41566:(e,t,r)=>{"use strict";r.d(t,{Q:()=>o});var o=(0,r(72926).tv)({base:[],variants:{orientation:{vertical:["overflow-y-auto","data-[top-scroll=true]:[mask-image:linear-gradient(0deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[bottom-scroll=true]:[mask-image:linear-gradient(180deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[top-bottom-scroll=true]:[mask-image:linear-gradient(#000,#000,transparent_0,#000_var(--scroll-shadow-size),#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]"],horizontal:["overflow-x-auto","data-[left-scroll=true]:[mask-image:linear-gradient(270deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[right-scroll=true]:[mask-image:linear-gradient(90deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[left-right-scroll=true]:[mask-image:linear-gradient(to_right,#000,#000,transparent_0,#000_var(--scroll-shadow-size),#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]"]},hideScrollBar:{true:"scrollbar-hide",false:""}},defaultVariants:{orientation:"vertical",hideScrollBar:!1}})},60899:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var o=r(37413),a=r(72873),s=r(23650),n=r(10974),l=r(11075),i=r(61120);let d=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),c=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let p=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:a="",children:s,iconNode:n,...l},d)=>(0,i.createElement)("svg",{ref:d,...u,width:t,height:t,stroke:e,strokeWidth:o?24*Number(r)/Number(t):r,className:c("lucide",a),...l},[...n.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(s)?s:[s]])),m=(e,t)=>{let r=(0,i.forwardRef)(({className:r,...o},a)=>(0,i.createElement)(p,{ref:a,iconNode:t,className:c(`lucide-${d(e)}`,r),...o}));return r.displayName=`${e}`,r},h=m("Siren",[["path",{d:"M7 18v-6a5 5 0 1 1 10 0v6",key:"pcx96s"}],["path",{d:"M5 21a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-1a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2z",key:"1b4s83"}],["path",{d:"M21 12h1",key:"jtio3y"}],["path",{d:"M18.5 4.5 18 5",key:"g5sp9y"}],["path",{d:"M2 12h1",key:"1uaihz"}],["path",{d:"M12 2v1",key:"11qlp1"}],["path",{d:"m4.929 4.929.707.707",key:"1i51kw"}],["path",{d:"M12 12v6",key:"3ahymv"}]]),f=m("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var x=r(4536),b=r.n(x);async function v(e){let t=await (await (0,n.G)("/emergency/?emergency_type=O",null,"GET")).json(),r=await (await (0,n.G)("/emergency/?emergency_type=M",null,"GET")).json(),i=await (await (0,n.G)("/emergency/?emergency_type=D",null,"GET")).json();return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"absolute w-full h-[75vh] top-0 pt-[35vh] bg-[url(/image_6.png)]",children:(0,o.jsxs)("div",{id:"send-emergency",className:"max-w-[30rem] drop-shadow-[#008524] bg-[#EEEEEE] mx-auto mb-24 py-8 px-6 rounded-xl flex flex-col items-center gap-3 relative bg-cover bg-center",children:[(0,o.jsx)(h,{className:"absolute top-3 left-3"}),(0,o.jsx)("h3",{children:"أرسل تنبيهًا للطوارئ"}),(0,o.jsx)("p",{children:"حدد إشعار الطوارئ ثم قم بملئ الحقول المطلوبة ومن ثم أرسل الطلب مباشرة وسيتم التوجة الى موقعكم في أسرع وقت ممكن."}),(0,o.jsx)(b(),{href:"/add-application",children:"Go"}),(0,o.jsx)(l.Button,{as:b(),href:"/add-application",endContent:(0,o.jsx)(f,{}),className:"mx-auto",color:"danger",children:"أرسل إشعار للطوارئ"})]})}),(0,o.jsxs)("section",{id:"recieved-emergency",className:"mt-[65vh]",children:[(0,o.jsx)("h2",{className:"text-2xl font-bold text-center",children:"إشعارات الطوارئ المستلمة"}),(0,o.jsx)(a.AlertSection,{data:t.results,heading:"طلبات التنبيه حول الإغاثة"}),(0,o.jsx)(a.AlertSection,{data:r.results,heading:"طلبات التنبيه حول الصحة"}),(0,o.jsx)(a.AlertSection,{data:i.results,heading:"طلبات التنبيه حول الخطر"})]}),(0,o.jsx)(s.default,{})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63257:(e,t,r)=>{"use strict";r.d(t,{Z:()=>N});var o=r(8710),a=r(72926),s=r(65146),n=(0,a.tv)({slots:{base:["flex","flex-col","relative","overflow-hidden","h-auto","outline-none","text-foreground","box-border","bg-content1",...s.zb],header:["flex","p-3","z-10","w-full","justify-start","items-center","shrink-0","overflow-inherit","color-inherit","subpixel-antialiased"],body:["relative","flex","flex-1","w-full","p-3","flex-auto","flex-col","place-content-inherit","align-items-inherit","h-auto","break-words","text-left","overflow-y-auto","subpixel-antialiased"],footer:["p-3","h-auto","flex","w-full","items-center","overflow-hidden","color-inherit","subpixel-antialiased"]},variants:{shadow:{none:{base:"shadow-none"},sm:{base:"shadow-small"},md:{base:"shadow-medium"},lg:{base:"shadow-large"}},radius:{none:{base:"rounded-none",header:"rounded-none",footer:"rounded-none"},sm:{base:"rounded-small",header:"rounded-t-small",footer:"rounded-b-small"},md:{base:"rounded-medium",header:"rounded-t-medium",footer:"rounded-b-medium"},lg:{base:"rounded-large",header:"rounded-t-large",footer:"rounded-b-large"}},fullWidth:{true:{base:"w-full"}},isHoverable:{true:{base:"data-[hover=true]:bg-content2 dark:data-[hover=true]:bg-content2"}},isPressable:{true:{base:"cursor-pointer"}},isBlurred:{true:{base:["bg-background/80","dark:bg-background/20","backdrop-blur-md","backdrop-saturate-150"]}},isFooterBlurred:{true:{footer:["bg-background/10","backdrop-blur","backdrop-saturate-150"]}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:"",false:{base:"transition-transform-background motion-reduce:transition-none"}}},compoundVariants:[{isPressable:!0,class:"data-[pressed=true]:scale-[0.97] tap-highlight-transparent"}],defaultVariants:{radius:"lg",shadow:"md",fullWidth:!1,isHoverable:!1,isPressable:!1,isDisabled:!1,isFooterBlurred:!1}}),l=r(43210),i=r(72406),d=r(25381),c=r(6409),u=r(40182),p=r(39217),m=r(55150),h=r(26109),f=r(16060),x=r(82432),b=r(1172),v=r(73094),g=r(54514),w=r(86925),j=r(81730),y=r(60687),k=(0,h.Rf)((e,t)=>{let{children:r,context:a,Component:s,isPressable:k,disableAnimation:N,disableRipple:C,getCardProps:G,getRippleProps:_}=function(e){var t,r,o,a;let s=(0,m.o)(),[j,y]=(0,h.rE)(e,n.variantKeys),{ref:k,as:N,children:C,onClick:G,onPress:_,autoFocus:E,className:P,classNames:A,allowTextSelectionOnPress:z=!0,...S}=j,B=(0,g.zD)(k),M=N||(e.isPressable?"button":"div"),I="string"==typeof M,$=null!=(r=null!=(t=e.disableAnimation)?t:null==s?void 0:s.disableAnimation)&&r,R=null!=(a=null!=(o=e.disableRipple)?o:null==s?void 0:s.disableRipple)&&a,W=(0,f.$)(null==A?void 0:A.base,P),{onClear:L,onPress:D,ripples:H}=(0,w.k)(),q=(0,l.useCallback)(e=>{R||$||B.current&&D(e)},[R,$,B,D]),{buttonProps:F,isPressed:U}=(0,p.l)({onPress:(0,i.c)(_,q),elementType:N,isDisabled:!e.isPressable,onClick:G,allowTextSelectionOnPress:z,...S},B),{hoverProps:Z,isHovered:T}=(0,u.M)({isDisabled:!e.isHoverable,...S}),{isFocusVisible:O,isFocused:V,focusProps:K}=(0,c.o)({autoFocus:E}),X=(0,l.useMemo)(()=>n({...y,disableAnimation:$}),[(0,x.t6)(y),$]),Q=(0,l.useMemo)(()=>({slots:X,classNames:A,disableAnimation:$,isDisabled:e.isDisabled,isFooterBlurred:e.isFooterBlurred,fullWidth:e.fullWidth}),[X,A,e.isDisabled,e.isFooterBlurred,$,e.fullWidth]),J=(0,l.useCallback)((t={})=>({ref:B,className:X.base({class:W}),tabIndex:e.isPressable?0:-1,"data-hover":(0,b.sE)(T),"data-pressed":(0,b.sE)(U),"data-focus":(0,b.sE)(V),"data-focus-visible":(0,b.sE)(O),"data-disabled":(0,b.sE)(e.isDisabled),...(0,d.v)(e.isPressable?{...F,...K,role:"button"}:{},e.isHoverable?Z:{},(0,v.$)(S,{enabled:I}),(0,v.$)(t))}),[B,X,W,I,e.isPressable,e.isHoverable,e.isDisabled,T,U,O,F,K,Z,S]),Y=(0,l.useCallback)(()=>({ripples:H,onClear:L}),[H,L]);return{context:Q,domRef:B,Component:M,classNames:A,children:C,isHovered:T,isPressed:U,disableAnimation:$,isPressable:e.isPressable,isHoverable:e.isHoverable,disableRipple:R,handlePress:q,isFocusVisible:O,getCardProps:J,getRippleProps:Y}}({...e,ref:t});return(0,y.jsxs)(s,{...G(),children:[(0,y.jsx)(o.u,{value:a,children:r}),k&&!N&&!C&&(0,y.jsx)(j.j,{..._()})]})});k.displayName="NextUI.Card";var N=k},72873:(e,t,r)=>{"use strict";r.d(t,{AlertSection:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AlertSection() from the server but AlertSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app\\frontend\\src\\components\\specific\\AlertSection\\index.tsx","AlertSection")},75995:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var o=r(65239),a=r(48088),s=r(88170),n=r.n(s),l=r(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let d={children:["",{children:["(home)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,60899)),"G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,89282)),"G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"G:\\Graduation project 2025\\app\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(home)/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79293:(e,t,r)=>{"use strict";r.d(t,{H:()=>c});var o=r(26109),a=r(41566),s=r(54514),n=r(82432),l=r(43210),i=r(60687),d=(0,o.Rf)((e,t)=>{let{Component:r,children:d,getBaseProps:c}=function(e){var t;let[r,i]=(0,o.rE)(e,a.Q.variantKeys),{ref:d,as:c,children:u,className:p,style:m,size:h=40,offset:f=0,visibility:x="auto",isEnabled:b=!0,onVisibilityChange:v,...g}=r,w=(0,s.zD)(d);!function(e={}){let{domRef:t,isEnabled:r=!0,overflowCheck:o="vertical",visibility:a="auto",offset:s=0,onVisibilityChange:i,updateDeps:d=[]}=e,c=(0,l.useRef)(a);(0,l.useEffect)(()=>{let e=null==t?void 0:t.current;if(!e||!r)return;let l=(t,r,o,s,l)=>{if("auto"===a){let t=`${s}${(0,n.ZH)(l)}Scroll`;r&&o?(e.dataset[t]="true",e.removeAttribute(`data-${s}-scroll`),e.removeAttribute(`data-${l}-scroll`)):(e.dataset[`${s}Scroll`]=r.toString(),e.dataset[`${l}Scroll`]=o.toString(),e.removeAttribute(`data-${s}-${l}-scroll`))}else{let e=r&&o?"both":r?s:o?l:"none";e!==c.current&&(null==i||i(e),c.current=e)}},d=()=>{for(let{type:t,prefix:r,suffix:a}of[{type:"vertical",prefix:"top",suffix:"bottom"},{type:"horizontal",prefix:"left",suffix:"right"}])if(o===t||"both"===o){let o="vertical"===t?e.scrollTop>s:e.scrollLeft>s,n="vertical"===t?e.scrollTop+e.clientHeight+s<e.scrollHeight:e.scrollLeft+e.clientWidth+s<e.scrollWidth;l(t,o,n,r,a)}},u=()=>{["top","bottom","top-bottom","left","right","left-right"].forEach(t=>{e.removeAttribute(`data-${t}-scroll`)})};return d(),e.addEventListener("scroll",d),"auto"!==a&&(u(),"both"===a?(e.dataset.topBottomScroll=String("vertical"===o),e.dataset.leftRightScroll=String("horizontal"===o)):(e.dataset.topBottomScroll="false",e.dataset.leftRightScroll="false",["top","bottom","left","right"].forEach(t=>{e.dataset[`${t}Scroll`]=String(a===t)}))),()=>{e.removeEventListener("scroll",d),u()}},[...d,r,a,o,i,t])}({domRef:w,offset:f,visibility:x,isEnabled:b,onVisibilityChange:v,updateDeps:[u],overflowCheck:null!=(t=e.orientation)?t:"vertical"});let j=(0,l.useMemo)(()=>(0,a.Q)({...i,className:p}),[(0,n.t6)(i),p]);return{Component:c||"div",styles:j,domRef:w,children:u,getBaseProps:(t={})=>{var r;return{ref:w,className:j,"data-orientation":null!=(r=e.orientation)?r:"vertical",style:{"--scroll-shadow-size":`${h}px`,...m,...t.style},...g,...t}}}}({...e,ref:t});return(0,i.jsx)(r,{...c(),children:d})});d.displayName="NextUI.ScrollShadow";var c=d},79551:e=>{"use strict";e.exports=require("url")},86760:(e,t,r)=>{"use strict";r.d(t,{U:()=>d});var o=r(8710),a=r(26109),s=r(54514),n=r(16060),l=r(60687),i=(0,a.Rf)((e,t)=>{var r;let{as:a,className:i,children:d,...c}=e,u=(0,s.zD)(t),{slots:p,classNames:m}=(0,o.f)(),h=(0,n.$)(null==m?void 0:m.body,i);return(0,l.jsx)(a||"div",{ref:u,className:null==(r=p.body)?void 0:r.call(p,{class:h}),...c,children:d})});i.displayName="NextUI.CardBody";var d=i},97598:(e,t,r)=>{Promise.resolve().then(r.bind(r,10529)),Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,31971)),Promise.resolve().then(r.bind(r,16784))}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,310,626,554,160,175,531,518],()=>r(75995));module.exports=o})();