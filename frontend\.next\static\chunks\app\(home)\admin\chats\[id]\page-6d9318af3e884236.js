(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[393],{24595:(e,s,a)=>{Promise.resolve().then(a.bind(a,99467))},27290:(e,s,a)=>{"use strict";a.d(s,{Z:()=>k});var r=a(65262),l=a(69478),t=a(66232),o=(0,l.tv)({slots:{base:["flex","flex-col","relative","overflow-hidden","h-auto","outline-none","text-foreground","box-border","bg-content1",...t.zb],header:["flex","p-3","z-10","w-full","justify-start","items-center","shrink-0","overflow-inherit","color-inherit","subpixel-antialiased"],body:["relative","flex","flex-1","w-full","p-3","flex-auto","flex-col","place-content-inherit","align-items-inherit","h-auto","break-words","text-left","overflow-y-auto","subpixel-antialiased"],footer:["p-3","h-auto","flex","w-full","items-center","overflow-hidden","color-inherit","subpixel-antialiased"]},variants:{shadow:{none:{base:"shadow-none"},sm:{base:"shadow-small"},md:{base:"shadow-medium"},lg:{base:"shadow-large"}},radius:{none:{base:"rounded-none",header:"rounded-none",footer:"rounded-none"},sm:{base:"rounded-small",header:"rounded-t-small",footer:"rounded-b-small"},md:{base:"rounded-medium",header:"rounded-t-medium",footer:"rounded-b-medium"},lg:{base:"rounded-large",header:"rounded-t-large",footer:"rounded-b-large"}},fullWidth:{true:{base:"w-full"}},isHoverable:{true:{base:"data-[hover=true]:bg-content2 dark:data-[hover=true]:bg-content2"}},isPressable:{true:{base:"cursor-pointer"}},isBlurred:{true:{base:["bg-background/80","dark:bg-background/20","backdrop-blur-md","backdrop-saturate-150"]}},isFooterBlurred:{true:{footer:["bg-background/10","backdrop-blur","backdrop-saturate-150"]}},isDisabled:{true:{base:"opacity-disabled cursor-not-allowed"}},disableAnimation:{true:"",false:{base:"transition-transform-background motion-reduce:transition-none"}}},compoundVariants:[{isPressable:!0,class:"data-[pressed=true]:scale-[0.97] tap-highlight-transparent"}],defaultVariants:{radius:"lg",shadow:"md",fullWidth:!1,isHoverable:!1,isPressable:!1,isDisabled:!1,isFooterBlurred:!1}}),d=a(12115),n=a(73750),i=a(81627),c=a(77151),u=a(9906),b=a(88629),m=a(75894),h=a(56973),x=a(5712),f=a(81467),v=a(672),g=a(491),p=a(6548),N=a(35925),y=a(9539),j=a(95155),w=(0,h.Rf)((e,s)=>{let{children:a,context:l,Component:t,isPressable:w,disableAnimation:k,disableRipple:C,getCardProps:P,getRippleProps:S}=function(e){var s,a,r,l;let t=(0,m.o)(),[y,j]=(0,h.rE)(e,o.variantKeys),{ref:w,as:k,children:C,onClick:P,onPress:S,autoFocus:E,className:D,classNames:H,allowTextSelectionOnPress:W=!0,..._}=y,R=(0,p.zD)(w),B=k||(e.isPressable?"button":"div"),I="string"==typeof B,z=null!=(a=null!=(s=e.disableAnimation)?s:null==t?void 0:t.disableAnimation)&&a,A=null!=(l=null!=(r=e.disableRipple)?r:null==t?void 0:t.disableRipple)&&l,F=(0,x.$)(null==H?void 0:H.base,D),{onClear:M,onPress:O,ripples:U}=(0,N.k)(),T=(0,d.useCallback)(e=>{A||z||R.current&&O(e)},[A,z,R,O]),{buttonProps:$,isPressed:V}=(0,b.l)({onPress:(0,n.c)(S,T),elementType:k,isDisabled:!e.isPressable,onClick:P,allowTextSelectionOnPress:W,..._},R),{hoverProps:G,isHovered:J}=(0,u.M)({isDisabled:!e.isHoverable,..._}),{isFocusVisible:Z,isFocused:q,focusProps:K}=(0,c.o)({autoFocus:E}),L=(0,d.useMemo)(()=>o({...j,disableAnimation:z}),[(0,f.t6)(j),z]),Q=(0,d.useMemo)(()=>({slots:L,classNames:H,disableAnimation:z,isDisabled:e.isDisabled,isFooterBlurred:e.isFooterBlurred,fullWidth:e.fullWidth}),[L,H,e.isDisabled,e.isFooterBlurred,z,e.fullWidth]),X=(0,d.useCallback)(function(){let s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:R,className:L.base({class:F}),tabIndex:e.isPressable?0:-1,"data-hover":(0,v.sE)(J),"data-pressed":(0,v.sE)(V),"data-focus":(0,v.sE)(q),"data-focus-visible":(0,v.sE)(Z),"data-disabled":(0,v.sE)(e.isDisabled),...(0,i.v)(e.isPressable?{...$,...K,role:"button"}:{},e.isHoverable?G:{},(0,g.$)(_,{enabled:I}),(0,g.$)(s))}},[R,L,F,I,e.isPressable,e.isHoverable,e.isDisabled,J,V,Z,$,K,G,_]),Y=(0,d.useCallback)(()=>({ripples:U,onClear:M}),[U,M]);return{context:Q,domRef:R,Component:B,classNames:H,children:C,isHovered:J,isPressed:V,disableAnimation:z,isPressable:e.isPressable,isHoverable:e.isHoverable,disableRipple:A,handlePress:T,isFocusVisible:Z,getCardProps:X,getRippleProps:Y}}({...e,ref:s});return(0,j.jsxs)(t,{...P(),children:[(0,j.jsx)(r.u,{value:l,children:a}),w&&!k&&!C&&(0,j.jsx)(y.j,{...S()})]})});w.displayName="NextUI.Card";var k=w},54736:(e,s,a)=>{"use strict";a.d(s,{U:()=>i});var r=a(65262),l=a(56973),t=a(6548),o=a(5712),d=a(95155),n=(0,l.Rf)((e,s)=>{var a;let{as:l,className:n,children:i,...c}=e,u=(0,t.zD)(s),{slots:b,classNames:m}=(0,r.f)(),h=(0,o.$)(null==m?void 0:m.body,n);return(0,d.jsx)(l||"div",{ref:u,className:null==(a=b.body)?void 0:a.call(b,{class:h}),...c,children:i})});n.displayName="NextUI.CardBody";var i=n},65262:(e,s,a)=>{"use strict";a.d(s,{f:()=>l,u:()=>r});var[r,l]=(0,a(42810).q)({name:"CardContext",strict:!0,errorMessage:"useCardContext: `context` is undefined. Seems you forgot to wrap component within <Card />"})},66575:(e,s,a)=>{"use strict";a.d(s,{d:()=>i});var r=a(65262),l=a(56973),t=a(6548),o=a(5712),d=a(95155),n=(0,l.Rf)((e,s)=>{var a;let{as:l,className:n,children:i,...c}=e,u=(0,t.zD)(s),{slots:b,classNames:m}=(0,r.f)(),h=(0,o.$)(null==m?void 0:m.header,n);return(0,d.jsx)(l||"div",{ref:u,className:null==(a=b.header)?void 0:a.call(b,{class:h}),...c,children:i})});n.displayName="NextUI.CardHeader";var i=n},99467:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>p});var r=a(95155),l=a(59434),t=a(90221);let o=(0,a(40157).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var d=a(73672),n=a(35695),i=a(12115),c=a(82842),u=a(62177),b=a(3668),m=a(27290),h=a(66575),x=a(54736),f=a(93176),v=a(66146),g=a(19713);function p(){var e;let[s]=(0,c.lT)(["access"]),a=(0,n.useParams)(),p=(0,n.useSearchParams)(),N=(0,n.useRouter)(),y=a.id,j=p.get("room"),w=(0,i.useRef)(null),[k,C]=(0,i.useState)([]),[P,S]=(0,i.useState)(null),[E,D]=(0,i.useState)(!1),H=(0,i.useRef)(null),W=s.access,_=(0,i.useContext)(g.UserContext),R=null==_||null==(e=_.user)?void 0:e.id,{control:B,handleSubmit:I,formState:{errors:z,isSubmitting:A},reset:F}=(0,u.mN)({resolver:(0,t.u)(b.y),defaultValues:{message:""}});if(!y||!j)return(0,n.notFound)();let M=()=>{var e;null==(e=w.current)||e.scrollIntoView({behavior:"smooth"})};return(0,i.useEffect)(()=>{if(W&&j){(0,l.G)("/chats/".concat(y,"/messages"),null,"GET",W).then(e=>e.json()).then(e=>{C(e.results)}).catch(e=>{console.error("Error fetching room info:",e)});let e=new WebSocket("ws://localhost:8000/ws/chat/".concat(j,"/?token=").concat(W));return H.current=e,e.onopen=()=>{console.log("WebSocket connected"),D(!0)},e.onmessage=s=>{let a=JSON.parse(s.data);console.log(a),C(e=>[{id:Math.random(),sender:a.user,body:a.message,chat_room:j,created_at:new Date().toISOString()},...e]),e.onclose=()=>{console.log("WebSocket disconnected"),D(!1)}},()=>{e.close()}}},[W,j]),(0,i.useEffect)(()=>{M()},[k]),(0,r.jsx)("div",{className:"container mx-auto py-6 max-w-4xl",children:(0,r.jsxs)(m.Z,{className:"border shadow-md h-[calc(100vh-100px)] flex flex-col",children:[(0,r.jsx)(h.d,{className:"bg-gray-50 dark:bg-gray-800 border-b px-4 py-3 flex flex-row items-center justify-between space-y-0",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(v.T,{onClick:()=>N.push("/admin/chats"),className:"mr-2",children:(0,r.jsx)(o,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-lg",children:"اسم المستخدم"}),(0,r.jsx)("div",{className:"flex items-center gap-2 mt-1",children:(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["رقم الغرفة: ",j]})})]})]})}),(0,r.jsx)(x.U,{className:"flex-1 fle overflow-y-auto p-4 space-y-4",children:0===k.length?(0,r.jsx)("div",{className:"grid place-content-center h-full",children:(0,r.jsx)("p",{className:"text-gray-500",children:"لا يوجد رسائل بعد"})}):(0,r.jsxs)("div",{className:"flex flex-col-reverse gap-3",children:[k.map(e=>(0,r.jsx)("div",{className:"flex ".concat(e.sender===R?"justify-end":"justify-start"),children:(0,r.jsxs)("div",{className:"max-w-[70%] px-4 py-2 rounded-lg ".concat(e.sender===R?"bg-blue-600 text-white rounded-br-none":"bg-gray-100 dark:bg-gray-800 rounded-bl-none"),children:[(0,r.jsx)("p",{className:"text-sm",children:e.body}),(0,r.jsx)("span",{className:"text-xs mt-1 block text-right ".concat(e.sender===R?"text-blue-100":"text-gray-500"),children:(0,l.f)(e.created_at)})]})},e.id)),(0,r.jsx)("div",{ref:w})]})}),(0,r.jsxs)("div",{className:"p-4 border-t",children:[(0,r.jsxs)("form",{className:"flex gap-2",onSubmit:I(e=>{var s;(null==(s=H.current)?void 0:s.readyState)===WebSocket.OPEN&&(H.current.send(JSON.stringify({message:e.message})),F())}),children:[(0,r.jsx)(u.xI,{name:"message",control:B,render:e=>{let{field:s}=e;return(0,r.jsx)(f.r,{...s,placeholder:"قم بكتابة رسالتك هنا...",className:"flex-1",disabled:!E})}}),(0,r.jsxs)(v.T,{type:"submit",disabled:A||!E,className:"bg-blue-600 hover:bg-blue-700 text-white",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"ارسال"]})]}),z.message&&(0,r.jsx)("p",{className:"text-red-500 text-xs mt-1",children:z.message.message})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[477,146,671,688,575,842,874,444,713,441,684,358],()=>s(24595)),_N_E=e.O()}]);