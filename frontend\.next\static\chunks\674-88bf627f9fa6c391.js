(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[674],{6425:(e,t,a)=>{"use strict";let i,n;a.d(t,{d:()=>aY});var o=a(75894),l=a(56973),r=a(69478),s=a(66232),c=(0,r.tv)({slots:{base:["group inline-flex flex-col relative"],label:["block","absolute","z-10","origin-top-left","rtl:origin-top-right","subpixel-antialiased","text-small","text-foreground-500","pointer-events-none"],mainWrapper:"w-full flex flex-col",trigger:"relative px-3 gap-3 w-full inline-flex flex-row items-center shadow-sm outline-none tap-highlight-transparent",innerWrapper:"inline-flex h-full w-[calc(100%_-_theme(spacing.6))] min-h-4 items-center gap-1.5 box-border",selectorIcon:"absolute end-3 w-4 h-4",spinner:"absolute end-3",value:["text-foreground-500","font-normal","w-full","text-start"],listboxWrapper:"scroll-py-6 w-full",listbox:"",popoverContent:"w-full p-1 overflow-hidden",helperWrapper:"p-1 flex relative flex-col gap-1.5",description:"text-tiny text-foreground-400",errorMessage:"text-tiny text-danger"},variants:{variant:{flat:{trigger:["bg-default-100","data-[hover=true]:bg-default-200","group-data-[focus=true]:bg-default-200"]},faded:{trigger:["bg-default-100","border-medium","border-default-200","data-[hover=true]:border-default-400 data-[focus=true]:border-default-400 data-[open=true]:border-default-400"],value:"group-data-[has-value=true]:text-default-foreground"},bordered:{trigger:["border-medium","border-default-200","data-[hover=true]:border-default-400","data-[open=true]:border-default-foreground","data-[focus=true]:border-default-foreground"],value:"group-data-[has-value=true]:text-default-foreground"},underlined:{trigger:["!px-1","!pb-0","!gap-0","relative","box-border","border-b-medium","shadow-[0_1px_0px_0_rgba(0,0,0,0.05)]","border-default-200","!rounded-none","hover:border-default-300","after:content-['']","after:w-0","after:origin-center","after:bg-default-foreground","after:absolute","after:left-1/2","after:-translate-x-1/2","after:-bottom-[2px]","after:h-[2px]","data-[open=true]:after:w-full","data-[focus=true]:after:w-full"],value:"group-data-[has-value=true]:text-default-foreground"}},color:{default:{},primary:{selectorIcon:"text-primary"},secondary:{selectorIcon:"text-secondary"},success:{selectorIcon:"text-success"},warning:{selectorIcon:"text-warning"},danger:{selectorIcon:"text-danger"}},size:{sm:{label:"text-tiny",trigger:"h-8 min-h-8 px-2 rounded-small",value:"text-small"},md:{trigger:"h-10 min-h-10 rounded-medium",value:"text-small"},lg:{trigger:"h-12 min-h-12 rounded-large",value:"text-medium"}},radius:{none:{trigger:"rounded-none"},sm:{trigger:"rounded-small"},md:{trigger:"rounded-medium"},lg:{trigger:"rounded-large"},full:{trigger:"rounded-full"}},labelPlacement:{outside:{base:"flex flex-col"},"outside-left":{base:"flex-row items-center flex-nowrap items-start",label:"relative pe-2 text-foreground"},inside:{label:"text-tiny cursor-pointer",trigger:"flex-col items-start justify-center gap-0"}},fullWidth:{true:{base:"w-full"},false:{base:"min-w-40"}},isDisabled:{true:{base:"opacity-disabled pointer-events-none",trigger:"pointer-events-none"}},isInvalid:{true:{label:"!text-danger",value:"!text-danger",selectorIcon:"text-danger"}},isRequired:{true:{label:"after:content-['*'] after:text-danger after:ms-0.5"}},isMultiline:{true:{label:"relative",trigger:"!h-auto"},false:{value:"truncate"}},disableAnimation:{true:{trigger:"after:transition-none",base:"transition-none",label:"transition-none",selectorIcon:"transition-none"},false:{base:"transition-background motion-reduce:transition-none !duration-150",label:["will-change-auto","origin-top-left","rtl:origin-top-right","!duration-200","!ease-out","transition-[transform,color,left,opacity]","motion-reduce:transition-none"],selectorIcon:"transition-transform duration-150 ease motion-reduce:transition-none"}},disableSelectorIconRotation:{true:{},false:{selectorIcon:"data-[open=true]:rotate-180"}}},defaultVariants:{variant:"flat",color:"default",size:"md",labelPlacement:"inside",fullWidth:!0,isDisabled:!1,isMultiline:!1,disableSelectorIconRotation:!1},compoundVariants:[{variant:"flat",color:"default",class:{value:"group-data-[has-value=true]:text-default-foreground",trigger:["bg-default-100","data-[hover=true]:bg-default-200"]}},{variant:"flat",color:"primary",class:{trigger:["bg-primary-100","text-primary","data-[hover=true]:bg-primary-50","group-data-[focus=true]:bg-primary-50"],value:"text-primary",label:"text-primary"}},{variant:"flat",color:"secondary",class:{trigger:["bg-secondary-100","text-secondary","data-[hover=true]:bg-secondary-50","group-data-[focus=true]:bg-secondary-50"],value:"text-secondary",label:"text-secondary"}},{variant:"flat",color:"success",class:{trigger:["bg-success-100","text-success-600","dark:text-success","data-[hover=true]:bg-success-50","group-data-[focus=true]:bg-success-50"],value:"text-success-600 dark:text-success",label:"text-success-600 dark:text-success"}},{variant:"flat",color:"warning",class:{trigger:["bg-warning-100","text-warning-600","dark:text-warning","data-[hover=true]:bg-warning-50","group-data-[focus=true]:bg-warning-50"],value:"text-warning-600 dark:text-warning",label:"text-warning-600 dark:text-warning"}},{variant:"flat",color:"danger",class:{trigger:["bg-danger-100","text-danger","dark:text-danger-500","data-[hover=true]:bg-danger-50","group-data-[focus=true]:bg-danger-50"],value:"text-danger dark:text-danger-500",label:"text-danger dark:text-danger-500"}},{variant:"faded",color:"primary",class:{trigger:"data-[hover=true]:border-primary data-[focus=true]:border-primary data-[open=true]:border-primary",label:"text-primary"}},{variant:"faded",color:"secondary",class:{trigger:"data-[hover=true]:border-secondary data-[focus=true]:border-secondary data-[open=true]:border-secondary",label:"text-secondary"}},{variant:"faded",color:"success",class:{trigger:"data-[hover=true]:border-success data-[focus=true]:border-success data-[open=true]:border-success",label:"text-success"}},{variant:"faded",color:"warning",class:{trigger:"data-[hover=true]:border-warning data-[focus=true]:border-warning data-[open=true]:border-warning",label:"text-warning"}},{variant:"faded",color:"danger",class:{trigger:"data-[hover=true]:border-danger data-[focus=true]:border-danger data-[open=true]:border-danger",label:"text-danger"}},{variant:"underlined",color:"default",class:{value:"group-data-[has-value=true]:text-foreground"}},{variant:"underlined",color:"primary",class:{trigger:"after:bg-primary",label:"text-primary"}},{variant:"underlined",color:"secondary",class:{trigger:"after:bg-secondary",label:"text-secondary"}},{variant:"underlined",color:"success",class:{trigger:"after:bg-success",label:"text-success"}},{variant:"underlined",color:"warning",class:{trigger:"after:bg-warning",label:"text-warning"}},{variant:"underlined",color:"danger",class:{trigger:"after:bg-danger",label:"text-danger"}},{variant:"bordered",color:"primary",class:{trigger:["data-[open=true]:border-primary","data-[focus=true]:border-primary"],label:"text-primary"}},{variant:"bordered",color:"secondary",class:{trigger:["data-[open=true]:border-secondary","data-[focus=true]:border-secondary"],label:"text-secondary"}},{variant:"bordered",color:"success",class:{trigger:["data-[open=true]:border-success","data-[focus=true]:border-success"],label:"text-success"}},{variant:"bordered",color:"warning",class:{trigger:["data-[open=true]:border-warning","data-[focus=true]:border-warning"],label:"text-warning"}},{variant:"bordered",color:"danger",class:{trigger:["data-[open=true]:border-danger","data-[focus=true]:border-danger"],label:"text-danger"}},{labelPlacement:"inside",color:"default",class:{label:"group-data-[filled=true]:text-default-600"}},{labelPlacement:"outside",color:"default",class:{label:"group-data-[filled=true]:text-foreground"}},{radius:"full",size:["sm"],class:{trigger:"px-3"}},{radius:"full",size:"md",class:{trigger:"px-4"}},{radius:"full",size:"lg",class:{trigger:"px-5"}},{disableAnimation:!1,variant:["faded","bordered"],class:{trigger:"transition-colors motion-reduce:transition-none"}},{disableAnimation:!1,variant:"underlined",class:{trigger:"after:transition-width motion-reduce:after:transition-none"}},{variant:["flat","faded"],class:{trigger:[...s.zb]}},{isInvalid:!0,variant:"flat",class:{trigger:["bg-danger-50","data-[hover=true]:bg-danger-100","group-data-[focus=true]:bg-danger-50"]}},{isInvalid:!0,variant:"bordered",class:{trigger:"!border-danger group-data-[focus=true]:border-danger"}},{isInvalid:!0,variant:"underlined",class:{trigger:"after:bg-danger"}},{labelPlacement:"inside",size:"sm",class:{trigger:"h-12 min-h-12 py-1.5 px-3"}},{labelPlacement:"inside",size:"md",class:{trigger:"h-14 min-h-14 py-2"}},{labelPlacement:"inside",size:"lg",class:{label:"text-medium",trigger:"h-16 min-h-16 py-2.5 gap-0"}},{labelPlacement:"outside",isMultiline:!1,class:{base:"group relative justify-end",label:["pb-0","z-20","top-1/2","-translate-y-1/2","group-data-[filled=true]:start-0"]}},{labelPlacement:["inside"],class:{label:"group-data-[filled=true]:scale-85"}},{labelPlacement:"inside",size:["sm","md"],class:{label:"text-small"}},{labelPlacement:"inside",isMultiline:!1,size:"sm",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.tiny)/2_-_8px)]"],innerWrapper:"group-data-[has-label=true]:pt-4"}},{labelPlacement:"inside",isMultiline:!1,size:"md",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_6px)]"],innerWrapper:"group-data-[has-label=true]:pt-4"}},{labelPlacement:"inside",isMultiline:!1,size:"lg",class:{label:["text-medium","group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_8px)]"],innerWrapper:"group-data-[has-label=true]:pt-5"}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"sm",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.tiny)/2_-_8px_-_theme(borderWidth.medium))]"]}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"md",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_6px_-_theme(borderWidth.medium))]"]}},{labelPlacement:"inside",variant:["faded","bordered"],isMultiline:!1,size:"lg",class:{label:["text-medium","group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_8px_-_theme(borderWidth.medium))]"]}},{labelPlacement:"inside",variant:"underlined",isMultiline:!1,size:"sm",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.tiny)/2_-_5px)]"]}},{labelPlacement:"inside",variant:"underlined",isMultiline:!1,size:"md",class:{label:["group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_3.5px)]"]}},{labelPlacement:"inside",variant:"underlined",isMultiline:!1,size:"lg",class:{label:["text-medium","group-data-[filled=true]:-translate-y-[calc(50%_+_theme(fontSize.small)/2_-_4px)]"]}},{labelPlacement:"outside",size:"sm",isMultiline:!1,class:{label:["start-2","text-tiny","group-data-[filled=true]:-translate-y-[calc(100%_+_theme(fontSize.tiny)/2_+_16px)]"],base:"data-[has-label=true]:mt-[calc(theme(fontSize.small)_+_8px)]"}},{labelPlacement:"outside",isMultiline:!1,size:"md",class:{label:["start-3","text-small","group-data-[filled=true]:-translate-y-[calc(100%_+_theme(fontSize.small)/2_+_20px)]"],base:"data-[has-label=true]:mt-[calc(theme(fontSize.small)_+_10px)]"}},{labelPlacement:"outside",isMultiline:!1,size:"lg",class:{label:["start-3","text-medium","group-data-[filled=true]:-translate-y-[calc(100%_+_theme(fontSize.small)/2_+_24px)]"],base:"data-[has-label=true]:mt-[calc(theme(fontSize.small)_+_12px)]"}},{labelPlacement:"outside",isMultiline:!0,class:{label:"pb-1.5"}},{labelPlacement:["inside","outside"],class:{label:["pe-2","max-w-full","text-ellipsis","overflow-hidden"]}}]}),p=a(6548),d=a(491),u=a(12115),m=a(88629),f=a(77151),g=a(5712),v=a(81467),h=a(672),x=a(81627),b=a(9906),y=a(42806),w=a(51828);function k(e){let[t,a]=(0,w.P)(e.isOpen,e.defaultOpen||!1,e.onOpenChange),i=(0,u.useCallback)(()=>{a(!0)},[a]),n=(0,u.useCallback)(()=>{a(!1)},[a]),o=(0,u.useCallback)(()=>{a(!t)},[a,t]);return{isOpen:t,setOpen:a,open:i,close:n,toggle:o}}var E=a(58258),z=a(51804);let D=new Map;function j(e){let{locale:t}=(0,z.Y)(),a=t+(e?Object.entries(e).sort((e,t)=>e[0]<t[0]?-1:1).join():"");if(D.has(a))return D.get(a);let i=new Intl.Collator(t,e);return D.set(a,i),i}var S=a(28944),C=a(51395),P={},O={},M={},A={},F={},R={},I={},T={},B={},N={},_={},L={},K={},W={},q={},H={},$={},V={},U={},Y={},X={},G={},J={},Z={},Q={},ee={},et={},ea={},ei={},en={},eo={},el={},er={},es={},ec={};ec={"ar-AE":{longPressMessage:`\u{627}\u{636}\u{63A}\u{637} \u{645}\u{637}\u{648}\u{644}\u{627}\u{64B} \u{623}\u{648} \u{627}\u{636}\u{63A}\u{637} \u{639}\u{644}\u{649} Alt + \u{627}\u{644}\u{633}\u{647}\u{645} \u{644}\u{623}\u{633}\u{641}\u{644} \u{644}\u{641}\u{62A}\u{62D} \u{627}\u{644}\u{642}\u{627}\u{626}\u{645}\u{629}`},"bg-BG":{longPressMessage:`\u{41D}\u{430}\u{442}\u{438}\u{441}\u{43D}\u{435}\u{442}\u{435} \u{43F}\u{440}\u{43E}\u{434}\u{44A}\u{43B}\u{436}\u{438}\u{442}\u{435}\u{43B}\u{43D}\u{43E} \u{438}\u{43B}\u{438} \u{43D}\u{430}\u{442}\u{438}\u{441}\u{43D}\u{435}\u{442}\u{435} Alt+ \u{441}\u{442}\u{440}\u{435}\u{43B}\u{43A}\u{430} \u{43D}\u{430}\u{434}\u{43E}\u{43B}\u{443}, \u{437}\u{430} \u{434}\u{430} \u{43E}\u{442}\u{432}\u{43E}\u{440}\u{438}\u{442}\u{435} \u{43C}\u{435}\u{43D}\u{44E}\u{442}\u{43E}`},"cs-CZ":{longPressMessage:`Dlouh\xfdm stiskem nebo stisknut\xedm kl\xe1ves Alt + \u{161}ipka dol\u{16F} otev\u{159}ete nab\xeddku`},"da-DK":{longPressMessage:`Langt tryk eller tryk p\xe5 Alt + pil ned for at \xe5bne menuen`},"de-DE":{longPressMessage:`Dr\xfccken Sie lange oder dr\xfccken Sie Alt + Nach-unten, um das Men\xfc zu \xf6ffnen`},"el-GR":{longPressMessage:`\u{3A0}\u{3B9}\u{3AD}\u{3C3}\u{3C4}\u{3B5} \u{3C0}\u{3B1}\u{3C1}\u{3B1}\u{3C4}\u{3B5}\u{3C4}\u{3B1}\u{3BC}\u{3AD}\u{3BD}\u{3B1} \u{3AE} \u{3C0}\u{3B1}\u{3C4}\u{3AE}\u{3C3}\u{3C4}\u{3B5} Alt + \u{3BA}\u{3AC}\u{3C4}\u{3C9} \u{3B2}\u{3AD}\u{3BB}\u{3BF}\u{3C2} \u{3B3}\u{3B9}\u{3B1} \u{3BD}\u{3B1} \u{3B1}\u{3BD}\u{3BF}\u{3AF}\u{3BE}\u{3B5}\u{3C4}\u{3B5} \u{3C4}\u{3BF} \u{3BC}\u{3B5}\u{3BD}\u{3BF}\u{3CD}`},"en-US":{longPressMessage:"Long press or press Alt + ArrowDown to open menu"},"es-ES":{longPressMessage:`Mantenga pulsado o pulse Alt + flecha abajo para abrir el men\xfa`},"et-EE":{longPressMessage:`Men\xfc\xfc avamiseks vajutage pikalt v\xf5i vajutage klahve Alt + allanool`},"fi-FI":{longPressMessage:`Avaa valikko painamalla pohjassa tai n\xe4pp\xe4inyhdistelm\xe4ll\xe4 Alt + Alanuoli`},"fr-FR":{longPressMessage:`Appuyez de mani\xe8re prolong\xe9e ou appuyez sur Alt\xa0+\xa0Fl\xe8che vers le bas pour ouvrir le menu.`},"he-IL":{longPressMessage:`\u{5DC}\u{5D7}\u{5E5} \u{5DC}\u{5D7}\u{5D9}\u{5E6}\u{5D4} \u{5D0}\u{5E8}\u{5D5}\u{5DB}\u{5D4} \u{5D0}\u{5D5} \u{5D4}\u{5E7}\u{5E9} Alt + ArrowDown \u{5DB}\u{5D3}\u{5D9} \u{5DC}\u{5E4}\u{5EA}\u{5D5}\u{5D7} \u{5D0}\u{5EA} \u{5D4}\u{5EA}\u{5E4}\u{5E8}\u{5D9}\u{5D8}`},"hr-HR":{longPressMessage:"Dugo pritisnite ili pritisnite Alt + strelicu prema dolje za otvaranje izbornika"},"hu-HU":{longPressMessage:`Nyomja meg hosszan, vagy nyomja meg az Alt + lefele ny\xedl gombot a men\xfc megnyit\xe1s\xe1hoz`},"it-IT":{longPressMessage:`Premere a lungo o premere Alt + Freccia gi\xf9 per aprire il menu`},"ja-JP":{longPressMessage:`\u{9577}\u{62BC}\u{3057}\u{307E}\u{305F}\u{306F} Alt+\u{4E0B}\u{77E2}\u{5370}\u{30AD}\u{30FC}\u{3067}\u{30E1}\u{30CB}\u{30E5}\u{30FC}\u{3092}\u{958B}\u{304F}`},"ko-KR":{longPressMessage:`\u{AE38}\u{AC8C} \u{B204}\u{B974}\u{AC70}\u{B098} Alt + \u{C544}\u{B798}\u{CABD} \u{D654}\u{C0B4}\u{D45C}\u{B97C} \u{B20C}\u{B7EC} \u{BA54}\u{B274} \u{C5F4}\u{AE30}`},"lt-LT":{longPressMessage:`Nor\u{117}dami atidaryti meniu, nuspaud\u{119} palaikykite arba paspauskite \u{201E}Alt + ArrowDown\u{201C}.`},"lv-LV":{longPressMessage:`Lai atv\u{113}rtu izv\u{113}lni, turiet nospiestu vai nospiediet tausti\u{146}u kombin\u{101}ciju Alt + lejupv\u{113}rst\u{101} bulti\u{146}a`},"nb-NO":{longPressMessage:`Langt trykk eller trykk Alt + PilNed for \xe5 \xe5pne menyen`},"nl-NL":{longPressMessage:"Druk lang op Alt + pijl-omlaag of druk op Alt om het menu te openen"},"pl-PL":{longPressMessage:`Naci\u{15B}nij i przytrzymaj lub naci\u{15B}nij klawisze Alt + Strza\u{142}ka w d\xf3\u{142}, aby otworzy\u{107} menu`},"pt-BR":{longPressMessage:"Pressione e segure ou pressione Alt + Seta para baixo para abrir o menu"},"pt-PT":{longPressMessage:"Prima continuamente ou prima Alt + Seta Para Baixo para abrir o menu"},"ro-RO":{longPressMessage:`Ap\u{103}sa\u{21B}i lung sau ap\u{103}sa\u{21B}i pe Alt + s\u{103}geat\u{103} \xeen jos pentru a deschide meniul`},"ru-RU":{longPressMessage:`\u{41D}\u{430}\u{436}\u{43C}\u{438}\u{442}\u{435} \u{438} \u{443}\u{434}\u{435}\u{440}\u{436}\u{438}\u{432}\u{430}\u{439}\u{442}\u{435} \u{438}\u{43B}\u{438} \u{43D}\u{430}\u{436}\u{43C}\u{438}\u{442}\u{435} Alt + \u{421}\u{442}\u{440}\u{435}\u{43B}\u{43A}\u{430} \u{432}\u{43D}\u{438}\u{437}, \u{447}\u{442}\u{43E}\u{431}\u{44B} \u{43E}\u{442}\u{43A}\u{440}\u{44B}\u{442}\u{44C} \u{43C}\u{435}\u{43D}\u{44E}`},"sk-SK":{longPressMessage:`Ponuku otvor\xedte dlh\xfdm stla\u{10D}en\xedm alebo stla\u{10D}en\xedm kl\xe1vesu Alt + kl\xe1vesu so \u{161}\xedpkou nadol`},"sl-SI":{longPressMessage:`Za odprtje menija pritisnite in dr\u{17E}ite gumb ali pritisnite Alt+pu\u{161}\u{10D}ica navzdol`},"sr-SP":{longPressMessage:"Dugo pritisnite ili pritisnite Alt + strelicu prema dole da otvorite meni"},"sv-SE":{longPressMessage:`H\xe5ll nedtryckt eller tryck p\xe5 Alt + pil ned\xe5t f\xf6r att \xf6ppna menyn`},"tr-TR":{longPressMessage:`Men\xfcy\xfc a\xe7mak i\xe7in uzun bas\u{131}n veya Alt + A\u{15F}a\u{11F}\u{131} Ok tu\u{15F}una bas\u{131}n`},"uk-UA":{longPressMessage:`\u{414}\u{43E}\u{432}\u{433}\u{43E} \u{430}\u{431}\u{43E} \u{437}\u{432}\u{438}\u{447}\u{430}\u{439}\u{43D}\u{43E} \u{43D}\u{430}\u{442}\u{438}\u{441}\u{43D}\u{456}\u{442}\u{44C} \u{43A}\u{43E}\u{43C}\u{431}\u{456}\u{43D}\u{430}\u{446}\u{456}\u{44E} \u{43A}\u{43B}\u{430}\u{432}\u{456}\u{448} Alt \u{456} \u{441}\u{442}\u{440}\u{456}\u{43B}\u{43A}\u{430} \u{432}\u{43D}\u{438}\u{437}, \u{449}\u{43E}\u{431} \u{432}\u{456}\u{434}\u{43A}\u{440}\u{438}\u{442}\u{438} \u{43C}\u{435}\u{43D}\u{44E}`},"zh-CN":{longPressMessage:`\u{957F}\u{6309}\u{6216}\u{6309} Alt + \u{5411}\u{4E0B}\u{65B9}\u{5411}\u{952E}\u{4EE5}\u{6253}\u{5F00}\u{83DC}\u{5355}`},"zh-TW":{longPressMessage:`\u{9577}\u{6309}\u{6216}\u{6309} Alt+\u{5411}\u{4E0B}\u{9375}\u{4EE5}\u{958B}\u{555F}\u{529F}\u{80FD}\u{8868}`}};var ep=a(35421);let ed=Symbol.for("react-aria.i18n.locale"),eu=Symbol.for("react-aria.i18n.strings");class em{getStringForLocale(e,t){let a=this.getStringsForLocale(t)[e];if(!a)throw Error(`Could not find intl message ${e} in ${t} locale`);return a}getStringsForLocale(e){let t=this.strings[e];return t||(t=function(e,t,a="en-US"){var i;if(t[e])return t[e];let n=(i=e,Intl.Locale?new Intl.Locale(i).language:i.split("-")[0]);if(t[n])return t[n];for(let e in t)if(e.startsWith(n+"-"))return t[e];return t[a]}(e,this.strings,this.defaultLocale),this.strings[e]=t),t}static getGlobalDictionaryForPackage(e){if("undefined"==typeof window)return null;let t=window[ed];if(void 0===n){let e=window[eu];if(!e)return null;for(let a in n={},e)n[a]=new em({[t]:e[a]},t)}let a=null==n?void 0:n[e];if(!a)throw Error(`Strings for package "${e}" were not included by LocalizedStringProvider. Please add it to the list passed to createLocalizedStringDictionary.`);return a}constructor(e,t="en-US"){this.strings=Object.fromEntries(Object.entries(e).filter(([,e])=>e)),this.defaultLocale=t}}let ef=new Map,eg=new Map;class ev{format(e,t){let a=this.strings.getStringForLocale(e,this.locale);return"function"==typeof a?a(t,this):a}plural(e,t,a="cardinal"){let i=t["="+e];if(i)return"function"==typeof i?i():i;let n=this.locale+":"+a,o=ef.get(n);return o||(o=new Intl.PluralRules(this.locale,{type:a}),ef.set(n,o)),"function"==typeof(i=t[o.select(e)]||t.other)?i():i}number(e){let t=eg.get(this.locale);return t||(t=new Intl.NumberFormat(this.locale),eg.set(this.locale,t)),t.format(e)}select(e,t){let a=e[t]||e.other;return"function"==typeof a?a():a}constructor(e,t){this.locale=e,this.strings=t}}let eh=new WeakMap;function ex(e,t){let a,{locale:i}=(0,z.Y)(),n=t&&em.getGlobalDictionaryForPackage(t)||((a=eh.get(e))||(a=new em(e),eh.set(e,a)),a);return(0,u.useMemo)(()=>new ev(i,n),[i,n])}var eb=a(38191);let ey=new WeakMap;function ew(e,t,a){let i,{type:n}=e,{isOpen:o}=t;(0,u.useEffect)(()=>{a&&a.current&&ey.set(a.current,t.close)}),"menu"===n?i=!0:"listbox"===n&&(i="listbox");let l=(0,ep.Bi)();return{triggerProps:{"aria-haspopup":i,"aria-expanded":o,"aria-controls":o?l:void 0,onPress:t.toggle},overlayProps:{id:l}}}class ek{getItemRect(e){let t=this.ref.current;if(!t)return null;let a=null!=e?t.querySelector(`[data-key="${CSS.escape(e.toString())}"]`):null;if(!a)return null;let i=t.getBoundingClientRect(),n=a.getBoundingClientRect();return{x:n.left-i.left+t.scrollLeft,y:n.top-i.top+t.scrollTop,width:n.width,height:n.height}}getContentSize(){var e,t;let a=this.ref.current;return{width:null!=(e=null==a?void 0:a.scrollWidth)?e:0,height:null!=(t=null==a?void 0:a.scrollHeight)?t:0}}getVisibleRect(){var e,t,a,i;let n=this.ref.current;return{x:null!=(e=null==n?void 0:n.scrollLeft)?e:0,y:null!=(t=null==n?void 0:n.scrollTop)?t:0,width:null!=(a=null==n?void 0:n.offsetWidth)?a:0,height:null!=(i=null==n?void 0:n.offsetHeight)?i:0}}constructor(e){this.ref=e}}var eE=a(9522);class ez{isDisabled(e){var t;return"all"===this.disabledBehavior&&((null==(t=e.props)?void 0:t.isDisabled)||this.disabledKeys.has(e.key))}findNextNonDisabled(e,t){let a=e;for(;null!=a;){let e=this.collection.getItem(a);if((null==e?void 0:e.type)==="item"&&!this.isDisabled(e))return a;a=t(a)}return null}getNextKey(e){let t=e;return t=this.collection.getKeyAfter(t),this.findNextNonDisabled(t,e=>this.collection.getKeyAfter(e))}getPreviousKey(e){let t=e;return t=this.collection.getKeyBefore(t),this.findNextNonDisabled(t,e=>this.collection.getKeyBefore(e))}findKey(e,t,a){let i=e,n=this.layoutDelegate.getItemRect(i);if(!n||null==i)return null;let o=n;do{if(null==(i=t(i)))break;n=this.layoutDelegate.getItemRect(i)}while(n&&a(o,n)&&null!=i);return i}isSameRow(e,t){return e.y===t.y||e.x!==t.x}isSameColumn(e,t){return e.x===t.x||e.y!==t.y}getKeyBelow(e){return"grid"===this.layout&&"vertical"===this.orientation?this.findKey(e,e=>this.getNextKey(e),this.isSameRow):this.getNextKey(e)}getKeyAbove(e){return"grid"===this.layout&&"vertical"===this.orientation?this.findKey(e,e=>this.getPreviousKey(e),this.isSameRow):this.getPreviousKey(e)}getNextColumn(e,t){return t?this.getPreviousKey(e):this.getNextKey(e)}getKeyRightOf(e){let t="ltr"===this.direction?"getKeyRightOf":"getKeyLeftOf";if(this.layoutDelegate[t])return e=this.layoutDelegate[t](e),this.findNextNonDisabled(e,e=>this.layoutDelegate[t](e));if("grid"===this.layout)if("vertical"===this.orientation)return this.getNextColumn(e,"rtl"===this.direction);else return this.findKey(e,e=>this.getNextColumn(e,"rtl"===this.direction),this.isSameColumn);return"horizontal"===this.orientation?this.getNextColumn(e,"rtl"===this.direction):null}getKeyLeftOf(e){let t="ltr"===this.direction?"getKeyLeftOf":"getKeyRightOf";if(this.layoutDelegate[t])return e=this.layoutDelegate[t](e),this.findNextNonDisabled(e,e=>this.layoutDelegate[t](e));if("grid"===this.layout)if("vertical"===this.orientation)return this.getNextColumn(e,"ltr"===this.direction);else return this.findKey(e,e=>this.getNextColumn(e,"ltr"===this.direction),this.isSameColumn);return"horizontal"===this.orientation?this.getNextColumn(e,"ltr"===this.direction):null}getFirstKey(){let e=this.collection.getFirstKey();return this.findNextNonDisabled(e,e=>this.collection.getKeyAfter(e))}getLastKey(){let e=this.collection.getLastKey();return this.findNextNonDisabled(e,e=>this.collection.getKeyBefore(e))}getKeyPageAbove(e){let t=this.ref.current,a=this.layoutDelegate.getItemRect(e);if(!a)return null;if(t&&!(0,eE.o)(t))return this.getFirstKey();let i=e;if("horizontal"===this.orientation){let e=Math.max(0,a.x+a.width-this.layoutDelegate.getVisibleRect().width);for(;a&&a.x>e&&null!=i;)a=null==(i=this.getKeyAbove(i))?null:this.layoutDelegate.getItemRect(i)}else{let e=Math.max(0,a.y+a.height-this.layoutDelegate.getVisibleRect().height);for(;a&&a.y>e&&null!=i;)a=null==(i=this.getKeyAbove(i))?null:this.layoutDelegate.getItemRect(i)}return null!=i?i:this.getFirstKey()}getKeyPageBelow(e){let t=this.ref.current,a=this.layoutDelegate.getItemRect(e);if(!a)return null;if(t&&!(0,eE.o)(t))return this.getLastKey();let i=e;if("horizontal"===this.orientation){let e=Math.min(this.layoutDelegate.getContentSize().width,a.y-a.width+this.layoutDelegate.getVisibleRect().width);for(;a&&a.x<e&&null!=i;)a=null==(i=this.getKeyBelow(i))?null:this.layoutDelegate.getItemRect(i)}else{let e=Math.min(this.layoutDelegate.getContentSize().height,a.y-a.height+this.layoutDelegate.getVisibleRect().height);for(;a&&a.y<e&&null!=i;)a=null==(i=this.getKeyBelow(i))?null:this.layoutDelegate.getItemRect(i)}return null!=i?i:this.getLastKey()}getKeyForSearch(e,t){if(!this.collator)return null;let a=this.collection,i=t||this.getFirstKey();for(;null!=i;){let t=a.getItem(i);if(!t)break;let n=t.textValue.slice(0,e.length);if(t.textValue&&0===this.collator.compare(n,e))return i;i=this.getNextKey(i)}return null}constructor(...e){if(1===e.length){let t=e[0];this.collection=t.collection,this.ref=t.ref,this.collator=t.collator,this.disabledKeys=t.disabledKeys||new Set,this.disabledBehavior=t.disabledBehavior||"all",this.orientation=t.orientation||"vertical",this.direction=t.direction,this.layout=t.layout||"stack",this.layoutDelegate=t.layoutDelegate||new ek(t.ref)}else this.collection=e[0],this.disabledKeys=e[1],this.ref=e[2],this.collator=e[3],this.layout="stack",this.orientation="vertical",this.disabledBehavior="all",this.layoutDelegate=new ek(this.ref);"stack"===this.layout&&"vertical"===this.orientation&&(this.getKeyLeftOf=void 0,this.getKeyRightOf=void 0)}}var eD=a(20555),ej=a(78257),eS=a(73750),eC=a(27905),eP=(e,t,a)=>{let i=null==t?void 0:t.current;if(!i||!i.contains(e)){let e=document.querySelectorAll("body > span[data-focus-scope-start]"),t=[];if(e.forEach(e=>{t.push(e.nextElementSibling)}),1===t.length)return a.close(),!1}return!i||!i.contains(e)},eO=a(81274),eM=a(64104),eA=new WeakMap,eF=a(71721),eR=a(62293);let eI={border:0,clip:"rect(0 0 0 0)",clipPath:"inset(50%)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"};function eT(e={}){let{style:t,isFocusable:a}=e,[i,n]=(0,u.useState)(!1),{focusWithinProps:o}=(0,eR.R)({isDisabled:!a,onFocusWithinChange:e=>n(e)}),l=(0,u.useMemo)(()=>i?t:t?{...eI,...t}:eI,[i]);return{visuallyHiddenProps:{...o,style:l}}}function eB(e){let{children:t,elementType:a="div",isFocusable:i,style:n,...o}=e,{visuallyHiddenProps:l}=eT(e);return u.createElement(a,(0,x.v)(o,l),t)}var eN=a(90602),e_=a(95155);function eL(e){var t;let{state:a,triggerRef:i,selectRef:n,label:o,name:l,isDisabled:r}=e,{containerProps:s,selectProps:c}=function(e,t,a){var i;let n=eA.get(t)||{},{autoComplete:o,name:l=n.name,isDisabled:r=n.isDisabled,selectionMode:s,onChange:c}=e,{validationBehavior:p,isRequired:d,isInvalid:u}=n,{visuallyHiddenProps:m}=eT();return(0,eF.F)(e.selectRef,t.selectedKeys,t.setSelectedKeys),(0,eN.X)({validationBehavior:p,focus:()=>{var e;return null==(e=a.current)?void 0:e.focus()}},t,e.selectRef),{containerProps:{...m,"aria-hidden":!0,"data-a11y-ignore":"aria-hidden-focus"},inputProps:{style:{display:"none"}},selectProps:{autoComplete:o,disabled:r,"aria-invalid":u||void 0,"aria-required":d&&"aria"===p||void 0,required:d&&"native"===p,name:l,tabIndex:-1,value:"multiple"===s?[...t.selectedKeys].map(e=>String(e)):null!=(i=[...t.selectedKeys][0])?i:"",multiple:"multiple"===s,onChange:e=>{t.setSelectedKeys(e.target.value),null==c||c(e)}}}}({...e,selectRef:n},a,i);return a.collection.size<=300?(0,e_.jsx)("div",{...s,"data-testid":"hidden-select-container",children:(0,e_.jsxs)("label",{children:[o,(0,e_.jsxs)("select",{...c,ref:n,children:[(0,e_.jsx)("option",{}),[...a.collection.getKeys()].map(e=>{let t=a.collection.getItem(e);if((null==t?void 0:t.type)==="item")return(0,e_.jsx)("option",{value:t.key,children:t.textValue},t.key)})]})]})}):l?(0,e_.jsx)("input",{autoComplete:c.autoComplete,disabled:r,name:l,type:"hidden",value:null!=(t=[...a.selectedKeys].join(","))?t:""}):null}let eK=new WeakMap;var eW=a(39117),eq=a(49388),eH=(0,r.tv)({slots:{base:"w-full relative flex flex-col gap-1 p-1 overflow-clip",list:"w-full flex flex-col gap-0.5 outline-none",emptyContent:["h-10","px-2","py-1.5","w-full","h-full","text-foreground-400","text-start"]}}),e$=(0,r.tv)({slots:{base:["flex","group","gap-2","items-center","justify-between","relative","px-2","py-1.5","w-full","h-full","box-border","rounded-small","subpixel-antialiased","outline-none","cursor-pointer","tap-highlight-transparent",...s.zb,"data-[focus-visible=true]:dark:ring-offset-background-content1"],wrapper:"w-full flex flex-col items-start justify-center",title:"flex-1 text-small font-normal",description:["w-full","text-tiny","text-foreground-500","group-hover:text-current"],selectedIcon:["text-inherit","w-3","h-3","flex-shrink-0"],shortcut:["px-1","py-0.5","rounded","font-sans","text-foreground-500","text-tiny","border-small","border-default-300","group-hover:border-current"]},variants:{variant:{solid:{base:""},bordered:{base:"border-medium border-transparent bg-transparent"},light:{base:"bg-transparent"},faded:{base:["border-small border-transparent hover:border-default data-[hover=true]:bg-default-100","data-[selectable=true]:focus:border-default data-[selectable=true]:focus:bg-default-100"]},flat:{base:""},shadow:{base:"data-[hover=true]:shadow-lg"}},color:{default:{},primary:{},secondary:{},success:{},warning:{},danger:{}},showDivider:{true:{base:["mb-1.5","after:content-['']","after:absolute","after:-bottom-1","after:left-0","after:right-0","after:h-divider","after:bg-divider"]},false:{}},isDisabled:{true:{base:"opacity-disabled pointer-events-none"}},disableAnimation:{true:{},false:{base:"data-[hover=true]:transition-colors"}},hasTitleTextChild:{true:{title:"truncate"}},hasDescriptionTextChild:{true:{description:"truncate"}}},defaultVariants:{variant:"solid",color:"default",showDivider:!1},compoundVariants:[{variant:"solid",color:"default",class:{base:["data-[hover=true]:bg-default","data-[hover=true]:text-default-foreground","data-[selectable=true]:focus:bg-default","data-[selectable=true]:focus:text-default-foreground"]}},{variant:"solid",color:"primary",class:{base:["data-[hover=true]:bg-primary data-[hover=true]:text-primary-foreground","data-[selectable=true]:focus:bg-primary data-[selectable=true]:focus:text-primary-foreground"]}},{variant:"solid",color:"secondary",class:{base:["data-[hover=true]:bg-secondary data-[hover=true]:text-secondary-foreground","data-[selectable=true]:focus:bg-secondary data-[selectable=true]:focus:text-secondary-foreground"]}},{variant:"solid",color:"success",class:{base:["data-[hover=true]:bg-success data-[hover=true]:text-success-foreground","data-[selectable=true]:focus:bg-success data-[selectable=true]:focus:text-success-foreground"]}},{variant:"solid",color:"warning",class:{base:["data-[hover=true]:bg-warning data-[hover=true]:text-warning-foreground","data-[selectable=true]:focus:bg-warning data-[selectable=true]:focus:text-warning-foreground"]}},{variant:"solid",color:"danger",class:{base:["data-[hover=true]:bg-danger data-[hover=true]:text-danger-foreground","data-[selectable=true]:focus:bg-danger data-[selectable=true]:focus:text-danger-foreground"]}},{variant:"shadow",color:"default",class:{base:["data-[hover=true]:shadow-default/50 data-[hover=true]:bg-default data-[hover=true]:text-default-foreground","data-[selectable=true]:focus:shadow-default/50 data-[selectable=true]:focus:bg-default data-[selectable=true]:focus:text-default-foreground"]}},{variant:"shadow",color:"primary",class:{base:["data-[hover=true]:shadow-primary/30 data-[hover=true]:bg-primary data-[hover=true]:text-primary-foreground","data-[selectable=true]:focus:shadow-primary/30 data-[selectable=true]:focus:bg-primary data-[selectable=true]:focus:text-primary-foreground"]}},{variant:"shadow",color:"secondary",class:{base:["data-[hover=true]:shadow-secondary/30 data-[hover=true]:bg-secondary data-[hover=true]:text-secondary-foreground","data-[selectable=true]:focus:shadow-secondary/30 data-[selectable=true]:focus:bg-secondary data-[selectable=true]:focus:text-secondary-foreground"]}},{variant:"shadow",color:"success",class:{base:["data-[hover=true]:shadow-success/30 data-[hover=true]:bg-success data-[hover=true]:text-success-foreground","data-[selectable=true]:focus:shadow-success/30 data-[selectable=true]:focus:bg-success data-[selectable=true]:focus:text-success-foreground"]}},{variant:"shadow",color:"warning",class:{base:["data-[hover=true]:shadow-warning/30 data-[hover=true]:bg-warning data-[hover=true]:text-warning-foreground","data-[selectable=true]:focus:shadow-warning/30 data-[selectable=true]:focus:bg-warning data-[selectable=true]:focus:text-warning-foreground"]}},{variant:"shadow",color:"danger",class:{base:["data-[hover=true]:shadow-danger/30 data-[hover=true]:bg-danger data-[hover=true]:text-danger-foreground","data-[selectable=true]:focus:shadow-danger/30 data-[selectable=true]:focus:bg-danger data-[selectable=true]:focus:text-danger-foreground"]}},{variant:"bordered",color:"default",class:{base:["data-[hover=true]:border-default","data-[selectable=true]:focus:border-default"]}},{variant:"bordered",color:"primary",class:{base:["data-[hover=true]:border-primary data-[hover=true]:text-primary","data-[selectable=true]:focus:border-primary data-[selectable=true]:focus:text-primary"]}},{variant:"bordered",color:"secondary",class:{base:["data-[hover=true]:border-secondary data-[hover=true]:text-secondary","data-[selectable=true]:focus:border-secondary data-[selectable=true]:focus:text-secondary"]}},{variant:"bordered",color:"success",class:{base:["data-[hover=true]:border-success data-[hover=true]:text-success","data-[selectable=true]:focus:border-success data-[selectable=true]:focus:text-success"]}},{variant:"bordered",color:"warning",class:{base:["data-[hover=true]:border-warning data-[hover=true]:text-warning","data-[selectable=true]:focus:border-warning data-[selectable=true]:focus:text-warning"]}},{variant:"bordered",color:"danger",class:{base:["data-[hover=true]:border-danger data-[hover=true]:text-danger","data-[selectable=true]:focus:border-danger data-[selectable=true]:focus:text-danger"]}},{variant:"flat",color:"default",class:{base:["data-[hover=true]:bg-default/40","data-[hover=true]:text-default-foreground","data-[selectable=true]:focus:bg-default/40","data-[selectable=true]:focus:text-default-foreground"]}},{variant:"flat",color:"primary",class:{base:["data-[hover=true]:bg-primary/20 data-[hover=true]:text-primary","data-[selectable=true]:focus:bg-primary/20 data-[selectable=true]:focus:text-primary"]}},{variant:"flat",color:"secondary",class:{base:["data-[hover=true]:bg-secondary/20 data-[hover=true]:text-secondary","data-[selectable=true]:focus:bg-secondary/20 data-[selectable=true]:focus:text-secondary"]}},{variant:"flat",color:"success",class:{base:["data-[hover=true]:bg-success/20 data-[hover=true]:text-success","data-[selectable=true]:focus:bg-success/20 data-[selectable=true]:focus:text-success"]}},{variant:"flat",color:"warning",class:{base:["data-[hover=true]:bg-warning/20 data-[hover=true]:text-warning","data-[selectable=true]:focus:bg-warning/20 data-[selectable=true]:focus:text-warning"]}},{variant:"flat",color:"danger",class:{base:["data-[hover=true]:bg-danger/20 data-[hover=true]:text-danger","data-[selectable=true]:focus:bg-danger/20 data-[selectable=true]:focus:text-danger"]}},{variant:"faded",color:"default",class:{base:["data-[hover=true]:text-default-foreground","data-[selectable=true]:focus:text-default-foreground"]}},{variant:"faded",color:"primary",class:{base:["data-[hover=true]:text-primary","data-[selectable=true]:focus:text-primary"]}},{variant:"faded",color:"secondary",class:{base:["data-[hover=true]:text-secondary","data-[selectable=true]:focus:text-secondary"]}},{variant:"faded",color:"success",class:{base:["data-[hover=true]:text-success","data-[selectable=true]:focus:text-success"]}},{variant:"faded",color:"warning",class:{base:["data-[hover=true]:text-warning","data-[selectable=true]:focus:text-warning"]}},{variant:"faded",color:"danger",class:{base:["data-[hover=true]:text-danger","data-[selectable=true]:focus:text-danger"]}},{variant:"light",color:"default",class:{base:["data-[hover=true]:text-default-500","data-[selectable=true]:focus:text-default-500"]}},{variant:"light",color:"primary",class:{base:["data-[hover=true]:text-primary","data-[selectable=true]:focus:text-primary"]}},{variant:"light",color:"secondary",class:{base:["data-[hover=true]:text-secondary","data-[selectable=true]:focus:text-secondary"]}},{variant:"light",color:"success",class:{base:["data-[hover=true]:text-success","data-[selectable=true]:focus:text-success"]}},{variant:"light",color:"warning",class:{base:["data-[hover=true]:text-warning","data-[selectable=true]:focus:text-warning"]}},{variant:"light",color:"danger",class:{base:["data-[hover=true]:text-danger","data-[selectable=true]:focus:text-danger"]}}]}),eV=(0,r.tv)({slots:{base:"relative mb-2",heading:"pl-1 text-tiny text-foreground-500",group:"data-[has-title=true]:pt-1",divider:"mt-2"}});function eU(e){let{isSelected:t,disableAnimation:a,...i}=e;return(0,e_.jsx)("svg",{"aria-hidden":"true","data-selected":t,role:"presentation",viewBox:"0 0 17 18",...i,children:(0,e_.jsx)("polyline",{fill:"none",points:"1 9 7 14 15 4",stroke:"currentColor",strokeDasharray:22,strokeDashoffset:t?44:66,strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,style:a?{}:{transition:"stroke-dashoffset 200ms ease"}})})}var eY=a(86176),eX=a(66680),eG=a(22989);let eJ=new WeakMap;var eZ=a(1126),eQ=a(19914),e0=a(44823),e1=e=>{let{Component:t,rendered:a,description:i,isSelectable:n,isSelected:r,isDisabled:s,selectedIcon:c,startContent:p,endContent:m,hideSelectedIcon:y,disableAnimation:w,getItemProps:k,getLabelProps:E,getWrapperProps:z,getDescriptionProps:D,getSelectedIconProps:j}=function(e){var t,a;let i=(0,o.o)(),[n,r]=(0,l.rE)(e,e$.variantKeys),{as:s,item:c,state:p,description:m,startContent:y,endContent:w,isVirtualized:k,selectedIcon:E,className:z,classNames:D,autoFocus:j,onPress:C,onClick:P,shouldHighlightOnFocus:O,hideSelectedIcon:M=!1,isReadOnly:A=!1,...F}=n,R=null!=(a=null!=(t=e.disableAnimation)?t:null==i?void 0:i.disableAnimation)&&a,I=(0,u.useRef)(null),T=s||(e.href?"a":"li"),B="string"==typeof T,{rendered:N,key:_}=c,L=p.disabledKeys.has(_)||e.isDisabled,K="none"!==p.selectionManager.selectionMode,W=!(0,e0.wR)()&&"undefined"!=typeof window&&window.screen.width<=700;P&&"function"==typeof P&&(0,eY.R)("onClick is deprecated, please use onPress instead. See: https://github.com/nextui-org/nextui/issues/4292","ListboxItem");let{pressProps:q,isPressed:H}=(0,eQ.d)({ref:I,isDisabled:L,onPress:C}),{isHovered:$,hoverProps:V}=(0,b.M)({isDisabled:L}),{isFocusVisible:U,focusProps:Y}=(0,f.o)({autoFocus:j}),{isFocused:X,isSelected:G,optionProps:J,labelProps:Z,descriptionProps:Q}=function(e,t,a){var i,n,o,l,r,s,c,p;let{key:d}=e,u=eK.get(t),m=null!=(o=e.isDisabled)?o:t.selectionManager.isDisabled(d),f=null!=(l=e.isSelected)?l:t.selectionManager.isSelected(d),g=null!=(r=e.shouldSelectOnPressUp)?r:null==u?void 0:u.shouldSelectOnPressUp,v=null!=(s=e.shouldFocusOnHover)?s:null==u?void 0:u.shouldFocusOnHover,h=null!=(c=e.shouldUseVirtualFocus)?c:null==u?void 0:u.shouldUseVirtualFocus,y=null!=(p=e.isVirtualized)?p:null==u?void 0:u.isVirtualized,w=(0,ep.X1)(),k=(0,ep.X1)(),E={role:"option","aria-disabled":m||void 0,"aria-selected":"none"!==t.selectionManager.selectionMode?f:void 0};(0,eX.cX)()&&(0,eX.Tc)()||(E["aria-label"]=e["aria-label"],E["aria-labelledby"]=w,E["aria-describedby"]=k);let z=t.collection.getItem(d);if(y){let e=Number(null==z?void 0:z.index);E["aria-posinset"]=Number.isNaN(e)?void 0:e+1,E["aria-setsize"]=function(e){let t=eJ.get(e);if(null!=t)return t;let a=0,i=t=>{for(let n of t)if("section"===n.type)i("function"==typeof e.getChildren?e.getChildren(n.key):n.childNodes);else a++};return i(e),eJ.set(e,a),a}(t.collection)}let D=(null==u?void 0:u.onAction)?()=>{var e;return null==u||null==(e=u.onAction)?void 0:e.call(u,d)}:void 0,{itemProps:j,isPressed:C,isFocused:P,hasAction:O,allowsSelection:M}=(0,eZ.p)({selectionManager:t.selectionManager,key:d,ref:a,shouldSelectOnPressUp:g,allowsDifferentPressOrigin:g&&v,isVirtualized:y,shouldUseVirtualFocus:h,isDisabled:m,onAction:D||(null==z||null==(i=z.props)?void 0:i.onAction)?(0,eS.c)(null==z||null==(n=z.props)?void 0:n.onAction,D):void 0,linkBehavior:null==u?void 0:u.linkBehavior}),{hoverProps:A}=(0,b.M)({isDisabled:m||!v,onHoverStart(){(0,S.pP)()||(t.selectionManager.setFocused(!0),t.selectionManager.setFocusedKey(d))}}),F=(0,ej.$)(null==z?void 0:z.props);delete F.id;let R=(0,eG._h)(null==z?void 0:z.props);return{optionProps:{...E,...(0,x.v)(F,j,A,R),id:function(e,t){let a=eK.get(e);if(!a)throw Error("Unknown list");return`${a.id}-option-${"string"==typeof t?t.replace(/\s*/g,""):""+t}`}(t,d)},labelProps:{id:w},descriptionProps:{id:k},isFocused:P,isFocusVisible:P&&(0,S.pP)(),isSelected:f,isDisabled:m,isPressed:C,allowsSelection:M,hasAction:O}}({key:_,isDisabled:L,"aria-label":n["aria-label"],isVirtualized:k},p,I),ee=J,et=(0,u.useMemo)(()=>e$({...r,isDisabled:L,disableAnimation:R,hasTitleTextChild:"string"==typeof N,hasDescriptionTextChild:"string"==typeof m}),[(0,v.t6)(r),L,R,N,m]),ea=(0,g.$)(null==D?void 0:D.base,z);A&&(ee=(0,v.GU)(ee));let ei=O&&X||(W?$||H:$||X&&!U),en=(0,u.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{"aria-hidden":(0,h.sE)(!0),"data-disabled":(0,h.sE)(L),className:et.selectedIcon({class:null==D?void 0:D.selectedIcon}),...e}},[L,et,D]);return{Component:T,domRef:I,slots:et,classNames:D,isSelectable:K,isSelected:G,isDisabled:L,rendered:N,description:m,startContent:y,endContent:w,selectedIcon:E,hideSelectedIcon:M,disableAnimation:R,getItemProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:I,...(0,x.v)({onClick:P},ee,A?{}:(0,x.v)(Y,q),V,(0,d.$)(F,{enabled:B}),e),"data-selectable":(0,h.sE)(K),"data-focus":(0,h.sE)(X),"data-hover":(0,h.sE)(ei),"data-disabled":(0,h.sE)(L),"data-selected":(0,h.sE)(G),"data-pressed":(0,h.sE)(H),"data-focus-visible":(0,h.sE)(U),className:et.base({class:(0,g.$)(ea,e.className)})}},getLabelProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{...(0,x.v)(Z,e),"data-label":(0,h.sE)(!0),className:et.title({class:null==D?void 0:D.title})}},getWrapperProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{...(0,x.v)(e),className:et.wrapper({class:null==D?void 0:D.wrapper})}},getDescriptionProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{...(0,x.v)(Q,e),className:et.description({class:null==D?void 0:D.description})}},getSelectedIconProps:en}}(e),C=(0,u.useMemo)(()=>{let e=(0,e_.jsx)(eU,{disableAnimation:w,isSelected:r});return"function"==typeof c?c({icon:e,isSelected:r,isDisabled:s}):c||e},[c,r,s,w]);return(0,e_.jsxs)(t,{...k(),children:[p,i?(0,e_.jsxs)("div",{...z(),children:[(0,e_.jsx)("span",{...E(),children:a}),(0,e_.jsx)("span",{...D(),children:i})]}):(0,e_.jsx)("span",{...E(),children:a}),n&&!y&&(0,e_.jsx)("span",{...j(),children:C}),m]})};e1.displayName="NextUI.ListboxItem";var e4=(0,r.tv)({base:"shrink-0 bg-divider border-none",variants:{orientation:{horizontal:"w-full h-divider",vertical:"h-full w-divider"}},defaultVariants:{orientation:"horizontal"}}),e3=(0,l.Rf)((e,t)=>{let{Component:a,getDividerProps:i}=function(e){var t;let a,i,{as:n,className:o,orientation:l,...r}=e,s=n||"hr";"hr"===s&&"vertical"===l&&(s="div");let{separatorProps:c}=(t={elementType:"string"==typeof s?s:"hr",orientation:l},i=(0,d.$)(t,{enabled:"string"==typeof t.elementType}),("vertical"===t.orientation&&(a="vertical"),"hr"!==t.elementType)?{separatorProps:{...i,role:"separator","aria-orientation":a}}:{separatorProps:i}),p=(0,u.useMemo)(()=>e4({orientation:l,className:o}),[l,o]);return{Component:s,getDividerProps:(0,u.useCallback)((e={})=>({className:p,role:"separator","data-orientation":l,...c,...r,...e}),[p,l,c,r])}}({...e});return(0,e_.jsx)(a,{ref:t,...i()})});e3.displayName="NextUI.Divider";var e2=(0,l.Rf)((e,t)=>{let{item:a,state:i,as:n,variant:o,color:l,disableAnimation:r,className:s,classNames:c,hideSelectedIcon:p,showDivider:d=!1,dividerProps:m={},itemClasses:f,title:v,items:h,...b}=e,y=(0,u.useMemo)(()=>eV(),[]),w=(0,g.$)(null==c?void 0:c.base,s),k=(0,g.$)(null==c?void 0:c.divider,null==m?void 0:m.className),{itemProps:E,headingProps:z,groupProps:D}=function(e){let{heading:t,"aria-label":a}=e,i=(0,ep.Bi)();return{itemProps:{role:"presentation"},headingProps:t?{id:i,role:"presentation"}:{},groupProps:{role:"group","aria-label":a,"aria-labelledby":t?i:void 0}}}({heading:a.rendered,"aria-label":a["aria-label"]});return(0,e_.jsxs)(n||"li",{"data-slot":"base",...(0,x.v)(E,b),className:y.base({class:w}),children:[a.rendered&&(0,e_.jsx)("span",{...z,className:y.heading({class:null==c?void 0:c.heading}),"data-slot":"heading",children:a.rendered}),(0,e_.jsxs)("ul",{...D,className:y.group({class:null==c?void 0:c.group}),"data-has-title":!!a.rendered,"data-slot":"group",children:[[...a.childNodes].map(e=>{let{key:t,props:a}=e,n=(0,e_.jsx)(e1,{classNames:f,color:l,disableAnimation:r,hideSelectedIcon:p,item:e,state:i,variant:o,...a},t);return e.wrapper&&(n=e.wrapper(n)),n}),d&&(0,e_.jsx)(e3,{as:"li",className:y.divider({class:k}),...m})]})]},a.key)});e2.displayName="NextUI.ListboxSection";var e5=a(47650);function e6(e,t,a){let i,n=a.initialDeps??[];return()=>{var o,l,r,s;let c,p;a.key&&(null==(o=a.debug)?void 0:o.call(a))&&(c=Date.now());let d=e();if(!(d.length!==n.length||d.some((e,t)=>n[t]!==e)))return i;if(n=d,a.key&&(null==(l=a.debug)?void 0:l.call(a))&&(p=Date.now()),i=t(...d),a.key&&(null==(r=a.debug)?void 0:r.call(a))){let e=Math.round((Date.now()-c)*100)/100,t=Math.round((Date.now()-p)*100)/100,i=t/16,n=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${n(t,5)} /${n(e,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*i,120))}deg 100% 31%);`,null==a?void 0:a.key)}return null==(s=null==a?void 0:a.onChange)||s.call(a,i),i}}function e7(e,t){if(void 0!==e)return e;throw Error(`Unexpected undefined${t?`: ${t}`:""}`)}let e8=(e,t)=>1>Math.abs(e-t),e9=(e,t,a)=>{let i;return function(...n){e.clearTimeout(i),i=e.setTimeout(()=>t.apply(this,n),a)}},te=e=>e,tt=e=>{let t=Math.max(e.startIndex-e.overscan,0),a=Math.min(e.endIndex+e.overscan,e.count-1),i=[];for(let e=t;e<=a;e++)i.push(e);return i},ta=(e,t)=>{let a=e.scrollElement;if(!a)return;let i=e.targetWindow;if(!i)return;let n=e=>{let{width:a,height:i}=e;t({width:Math.round(a),height:Math.round(i)})};if(n(a.getBoundingClientRect()),!i.ResizeObserver)return()=>{};let o=new i.ResizeObserver(e=>{let t=e[0];if(null==t?void 0:t.borderBoxSize){let e=t.borderBoxSize[0];if(e)return void n({width:e.inlineSize,height:e.blockSize})}n(a.getBoundingClientRect())});return o.observe(a,{box:"border-box"}),()=>{o.unobserve(a)}},ti={passive:!0},tn="undefined"==typeof window||"onscrollend"in window,to=(e,t)=>{let a=e.scrollElement;if(!a)return;let i=e.targetWindow;if(!i)return;let n=0,o=e.options.useScrollendEvent&&tn?()=>void 0:e9(i,()=>{t(n,!1)},e.options.isScrollingResetDelay),l=i=>()=>{let{horizontal:l,isRtl:r}=e.options;n=l?a.scrollLeft*(r&&-1||1):a.scrollTop,o(),t(n,i)},r=l(!0),s=l(!1);return s(),a.addEventListener("scroll",r,ti),a.addEventListener("scrollend",s,ti),()=>{a.removeEventListener("scroll",r),a.removeEventListener("scrollend",s)}},tl=(e,t,a)=>{if(null==t?void 0:t.borderBoxSize){let e=t.borderBoxSize[0];if(e)return Math.round(e[a.options.horizontal?"inlineSize":"blockSize"])}return Math.round(e.getBoundingClientRect()[a.options.horizontal?"width":"height"])},tr=(e,{adjustments:t=0,behavior:a},i)=>{var n,o;null==(o=null==(n=i.scrollElement)?void 0:n.scrollTo)||o.call(n,{[i.options.horizontal?"left":"top"]:e+t,behavior:a})};class ts{constructor(e){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let e=null,t=()=>e||(this.targetWindow&&this.targetWindow.ResizeObserver?e=new this.targetWindow.ResizeObserver(e=>{e.forEach(e=>{this._measureElement(e.target,e)})}):null);return{disconnect:()=>{var a;null==(a=t())||a.disconnect(),e=null},observe:e=>{var a;return null==(a=t())?void 0:a.observe(e,{box:"border-box"})},unobserve:e=>{var a;return null==(a=t())?void 0:a.unobserve(e)}}})(),this.range=null,this.setOptions=e=>{Object.entries(e).forEach(([t,a])=>{void 0===a&&delete e[t]}),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:te,rangeExtractor:tt,onChange:()=>{},measureElement:tl,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!0,...e}},this.notify=e=>{var t,a;null==(a=(t=this.options).onChange)||a.call(t,this,e)},this.maybeNotify=e6(()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]),e=>{this.notify(e)},{key:!1,debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach(e=>e()),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var e;let t=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==t){if(this.cleanup(),!t)return void this.maybeNotify();this.scrollElement=t,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=(null==(e=this.scrollElement)?void 0:e.window)??null,this.elementsCache.forEach(e=>{this.observer.observe(e)}),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,e=>{this.scrollRect=e,this.maybeNotify()})),this.unsubs.push(this.options.observeElementOffset(this,(e,t)=>{this.scrollAdjustments=0,this.scrollDirection=t?this.getScrollOffset()<e?"forward":"backward":null,this.scrollOffset=e,this.isScrolling=t,this.maybeNotify()}))}},this.getSize=()=>this.options.enabled?(this.scrollRect=this.scrollRect??this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0),this.getScrollOffset=()=>this.options.enabled?(this.scrollOffset=this.scrollOffset??("function"==typeof this.options.initialOffset?this.options.initialOffset():this.options.initialOffset),this.scrollOffset):(this.scrollOffset=null,0),this.getFurthestMeasurement=(e,t)=>{let a=new Map,i=new Map;for(let n=t-1;n>=0;n--){let t=e[n];if(a.has(t.lane))continue;let o=i.get(t.lane);if(null==o||t.end>o.end?i.set(t.lane,t):t.end<o.end&&a.set(t.lane,!0),a.size===this.options.lanes)break}return i.size===this.options.lanes?Array.from(i.values()).sort((e,t)=>e.end===t.end?e.index-t.index:e.end-t.end)[0]:void 0},this.getMeasurementOptions=e6(()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled],(e,t,a,i,n)=>(this.pendingMeasuredCacheIndexes=[],{count:e,paddingStart:t,scrollMargin:a,getItemKey:i,enabled:n}),{key:!1}),this.getMeasurements=e6(()=>[this.getMeasurementOptions(),this.itemSizeCache],({count:e,paddingStart:t,scrollMargin:a,getItemKey:i,enabled:n},o)=>{if(!n)return this.measurementsCache=[],this.itemSizeCache.clear(),[];0===this.measurementsCache.length&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(e=>{this.itemSizeCache.set(e.key,e.size)}));let l=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];let r=this.measurementsCache.slice(0,l);for(let n=l;n<e;n++){let e=i(n),l=1===this.options.lanes?r[n-1]:this.getFurthestMeasurement(r,n),s=l?l.end+this.options.gap:t+a,c=o.get(e),p="number"==typeof c?c:this.options.estimateSize(n),d=s+p,u=l?l.lane:n%this.options.lanes;r[n]={index:n,start:s,size:p,end:d,key:e,lane:u}}return this.measurementsCache=r,r},{key:!1,debug:()=>this.options.debug}),this.calculateRange=e6(()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset()],(e,t,a)=>this.range=e.length>0&&t>0?function({measurements:e,outerSize:t,scrollOffset:a}){let i=e.length-1,n=tc(0,i,t=>e[t].start,a),o=n;for(;o<i&&e[o].end<a+t;)o++;return{startIndex:n,endIndex:o}}({measurements:e,outerSize:t,scrollOffset:a}):null,{key:!1,debug:()=>this.options.debug}),this.getIndexes=e6(()=>[this.options.rangeExtractor,this.calculateRange(),this.options.overscan,this.options.count],(e,t,a,i)=>null===t?[]:e({startIndex:t.startIndex,endIndex:t.endIndex,overscan:a,count:i}),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=e=>{let t=this.options.indexAttribute,a=e.getAttribute(t);return a?parseInt(a,10):(console.warn(`Missing attribute name '${t}={index}' on measured element.`),-1)},this._measureElement=(e,t)=>{let a=this.indexFromElement(e),i=this.measurementsCache[a];if(!i)return;let n=i.key,o=this.elementsCache.get(n);o!==e&&(o&&this.observer.unobserve(o),this.observer.observe(e),this.elementsCache.set(n,e)),e.isConnected&&this.resizeItem(a,this.options.measureElement(e,t,this))},this.resizeItem=(e,t)=>{let a=this.measurementsCache[e];if(!a)return;let i=t-(this.itemSizeCache.get(a.key)??a.size);0!==i&&((void 0!==this.shouldAdjustScrollPositionOnItemSizeChange?this.shouldAdjustScrollPositionOnItemSizeChange(a,i,this):a.start<this.getScrollOffset()+this.scrollAdjustments)&&this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=i,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(a.index),this.itemSizeCache=new Map(this.itemSizeCache.set(a.key,t)),this.notify(!1))},this.measureElement=e=>{if(!e)return void this.elementsCache.forEach((e,t)=>{e.isConnected||(this.observer.unobserve(e),this.elementsCache.delete(t))});this._measureElement(e,void 0)},this.getVirtualItems=e6(()=>[this.getIndexes(),this.getMeasurements()],(e,t)=>{let a=[];for(let i=0,n=e.length;i<n;i++){let n=t[e[i]];a.push(n)}return a},{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=e=>{let t=this.getMeasurements();if(0!==t.length)return e7(t[tc(0,t.length-1,e=>e7(t[e]).start,e)])},this.getOffsetForAlignment=(e,t)=>{let a=this.getSize(),i=this.getScrollOffset();"auto"===t&&e>=i+a&&(t="end"),"end"===t&&(e-=a);let n=this.options.horizontal?"scrollWidth":"scrollHeight";return Math.max(Math.min((this.scrollElement?"document"in this.scrollElement?this.scrollElement.document.documentElement[n]:this.scrollElement[n]:0)-a,e),0)},this.getOffsetForIndex=(e,t="auto")=>{e=Math.max(0,Math.min(e,this.options.count-1));let a=this.measurementsCache[e];if(!a)return;let i=this.getSize(),n=this.getScrollOffset();if("auto"===t)if(a.end>=n+i-this.options.scrollPaddingEnd)t="end";else{if(!(a.start<=n+this.options.scrollPaddingStart))return[n,t];t="start"}let o=a.start-this.options.scrollPaddingStart+(a.size-i)/2;switch(t){case"center":return[this.getOffsetForAlignment(o,t),t];case"end":return[this.getOffsetForAlignment(a.end+this.options.scrollPaddingEnd,t),t];default:return[this.getOffsetForAlignment(a.start-this.options.scrollPaddingStart,t),t]}},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{null!==this.scrollToIndexTimeoutId&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(e,{align:t="start",behavior:a}={})=>{this.cancelScrollToIndex(),"smooth"===a&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(e,t),{adjustments:void 0,behavior:a})},this.scrollToIndex=(e,{align:t="auto",behavior:a}={})=>{e=Math.max(0,Math.min(e,this.options.count-1)),this.cancelScrollToIndex(),"smooth"===a&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");let i=this.getOffsetForIndex(e,t);if(!i)return;let[n,o]=i;this._scrollToOffset(n,{adjustments:void 0,behavior:a}),"smooth"!==a&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout(()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(e))){let[t]=e7(this.getOffsetForIndex(e,o));e8(t,this.getScrollOffset())||this.scrollToIndex(e,{align:o,behavior:a})}else this.scrollToIndex(e,{align:o,behavior:a})}))},this.scrollBy=(e,{behavior:t}={})=>{this.cancelScrollToIndex(),"smooth"===t&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+e,{adjustments:void 0,behavior:t})},this.getTotalSize=()=>{var e;let t,a=this.getMeasurements();return Math.max((0===a.length?this.options.paddingStart:1===this.options.lanes?(null==(e=a[a.length-1])?void 0:e.end)??0:Math.max(...a.slice(-this.options.lanes).map(e=>e.end)))-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(e,{adjustments:t,behavior:a})=>{this.options.scrollToFn(e,{behavior:a,adjustments:t},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(e)}}let tc=(e,t,a,i)=>{for(;e<=t;){let n=(e+t)/2|0,o=a(n);if(o<i)e=n+1;else{if(!(o>i))return n;t=n-1}}return e>0?e-1:0},tp="undefined"!=typeof document?u.useLayoutEffect:u.useEffect;var td=a(47956),tu=(e,t)=>{let a=[];for(let i of e)"section"===i.type?a.push(([...i.childNodes].length+1)*t):a.push(t);return a},tm=e=>{if(!e||void 0===e.scrollTop||void 0===e.clientHeight||void 0===e.scrollHeight)return{isTop:!1,isBottom:!1,isMiddle:!1};let t=0===e.scrollTop,a=Math.ceil(e.scrollTop+e.clientHeight)>=e.scrollHeight;return{isTop:t,isBottom:a,isMiddle:!t&&!a}},tf=e=>{let{Component:t,state:a,color:i,variant:n,itemClasses:o,getBaseProps:r,topContent:s,bottomContent:c,hideEmptyContent:m,hideSelectedIcon:f,shouldHighlightOnFocus:g,disableAnimation:b,getEmptyContentProps:y,getListProps:w,scrollShadowProps:k}=e,{virtualization:E}=e;if(!E||!(0,h.Im)(E)&&!E.maxListboxHeight&&!E.itemHeight)throw Error("You are using a virtualized listbox. VirtualizedListbox requires 'virtualization' props with 'maxListboxHeight' and 'itemHeight' properties. This error might have originated from autocomplete components that use VirtualizedListbox. Please provide these props to use the virtualized listbox.");let{maxListboxHeight:z,itemHeight:D}=E,j=Math.min(z,D*a.collection.size),S=(0,u.useRef)(null),C=(0,u.useMemo)(()=>tu([...a.collection],D),[a.collection,D]),P=function(e){let t=u.useReducer(()=>({}),{})[1],a={...e,onChange:(a,i)=>{var n;i?(0,e5.flushSync)(t):t(),null==(n=e.onChange)||n.call(e,a,i)}},[i]=u.useState(()=>new ts(a));return i.setOptions(a),tp(()=>i._didMount(),[]),tp(()=>i._willUpdate()),i}({observeElementRect:ta,observeElementOffset:to,scrollToFn:tr,...{count:[...a.collection].length,getScrollElement:()=>S.current,estimateSize:e=>C[e]}}),O=P.getVirtualItems(),{getBaseProps:M}=function(e){var t;let[a,i]=(0,l.rE)(e,td.Q.variantKeys),{ref:n,as:o,children:r,className:s,style:c,size:d=40,offset:m=0,visibility:f="auto",isEnabled:g=!0,onVisibilityChange:h,...x}=a,b=(0,p.zD)(n);!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{domRef:t,isEnabled:a=!0,overflowCheck:i="vertical",visibility:n="auto",offset:o=0,onVisibilityChange:l,updateDeps:r=[]}=e,s=(0,u.useRef)(n);(0,u.useEffect)(()=>{let e=null==t?void 0:t.current;if(!e||!a)return;let r=(t,a,i,o,r)=>{if("auto"===n){let t="".concat(o).concat((0,v.ZH)(r),"Scroll");a&&i?(e.dataset[t]="true",e.removeAttribute("data-".concat(o,"-scroll")),e.removeAttribute("data-".concat(r,"-scroll"))):(e.dataset["".concat(o,"Scroll")]=a.toString(),e.dataset["".concat(r,"Scroll")]=i.toString(),e.removeAttribute("data-".concat(o,"-").concat(r,"-scroll")))}else{let e=a&&i?"both":a?o:i?r:"none";e!==s.current&&(null==l||l(e),s.current=e)}},c=()=>{for(let{type:t,prefix:a,suffix:n}of[{type:"vertical",prefix:"top",suffix:"bottom"},{type:"horizontal",prefix:"left",suffix:"right"}])if(i===t||"both"===i){let i="vertical"===t?e.scrollTop>o:e.scrollLeft>o,l="vertical"===t?e.scrollTop+e.clientHeight+o<e.scrollHeight:e.scrollLeft+e.clientWidth+o<e.scrollWidth;r(t,i,l,a,n)}},p=()=>{["top","bottom","top-bottom","left","right","left-right"].forEach(t=>{e.removeAttribute("data-".concat(t,"-scroll"))})};return c(),e.addEventListener("scroll",c),"auto"!==n&&(p(),"both"===n?(e.dataset.topBottomScroll=String("vertical"===i),e.dataset.leftRightScroll=String("horizontal"===i)):(e.dataset.topBottomScroll="false",e.dataset.leftRightScroll="false",["top","bottom","left","right"].forEach(t=>{e.dataset["".concat(t,"Scroll")]=String(n===t)}))),()=>{e.removeEventListener("scroll",c),p()}},[...r,a,n,i,l,t])}({domRef:b,offset:m,visibility:f,isEnabled:g,onVisibilityChange:h,updateDeps:[r],overflowCheck:null!=(t=e.orientation)?t:"vertical"});let y=(0,u.useMemo)(()=>(0,td.Q)({...i,className:s}),[(0,v.t6)(i),s]);return{Component:o||"div",styles:y,domRef:b,children:r,getBaseProps:function(){var t;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:b,className:y,"data-orientation":null!=(t=e.orientation)?t:"vertical",style:{"--scroll-shadow-size":"".concat(d,"px"),...c,...a.style},...x,...a}}}}({...k}),A=e=>{var t;let l=[...a.collection][e.index];if(!l)return null;let r={color:i,item:l,state:a,variant:n,disableAnimation:b,hideSelectedIcon:f,...l.props},s={position:"absolute",top:0,left:0,width:"100%",height:"".concat(e.size,"px"),transform:"translateY(".concat(e.start,"px)")};if("section"===l.type)return(0,e_.jsx)(e2,{...r,itemClasses:o,style:{...s,...r.style}},l.key);let c=(0,e_.jsx)(e1,{...r,classNames:(0,x.v)(o,null==(t=l.props)?void 0:t.classNames),shouldHighlightOnFocus:g,style:{...s,...r.style}},l.key);return l.wrapper&&(c=l.wrapper(c)),c},[F,R]=(0,u.useState)({isTop:!1,isBottom:!0,isMiddle:!1}),I=(0,e_.jsxs)(t,{...w(),children:[!a.collection.size&&!m&&(0,e_.jsx)("li",{children:(0,e_.jsx)("div",{...y()})}),(0,e_.jsx)("div",{...(0,d.$)(M()),ref:S,"data-bottom-scroll":F.isTop,"data-top-bottom-scroll":F.isMiddle,"data-top-scroll":F.isBottom,style:{height:z,overflow:"auto"},onScroll:e=>{R(tm(e.target))},children:j>0&&D>0&&(0,e_.jsx)("div",{style:{height:"".concat(P.getTotalSize(),"px"),width:"100%",position:"relative"},children:O.map(e=>A(e))})})]});return(0,e_.jsxs)("div",{...r(),children:[s,I,c]})},tg=(0,l.Rf)(function(e,t){let{isVirtualized:a,...i}=e,n=function(e){var t;let a=(0,o.o)(),{ref:i,as:n,state:l,variant:r,color:s,onAction:c,children:m,onSelectionChange:f,disableAnimation:v=null!=(t=null==a?void 0:a.disableAnimation)&&t,itemClasses:h,className:b,topContent:w,bottomContent:k,emptyContent:E="No items.",hideSelectedIcon:z=!1,hideEmptyContent:D=!1,shouldHighlightOnFocus:S=!1,classNames:C,...P}=e,O=n||"ul",M="string"==typeof O,A=(0,p.zD)(i),F=(0,y.p)({...e,children:m,onSelectionChange:f}),R=l||F,{listBoxProps:I}=function(e,t,a){let i=(0,ej.$)(e,{labelable:!0}),n=e.selectionBehavior||"toggle",o=e.linkBehavior||("replace"===n?"action":"override");"toggle"===n&&"action"===o&&(o="override");let{listProps:l}=function(e){let{selectionManager:t,collection:a,disabledKeys:i,ref:n,keyboardDelegate:o,layoutDelegate:l}=e,r=j({usage:"search",sensitivity:"base"}),s=t.disabledBehavior,c=(0,u.useMemo)(()=>o||new ez({collection:a,disabledKeys:i,disabledBehavior:s,ref:n,collator:r,layoutDelegate:l}),[o,l,a,i,n,r,s]),{collectionProps:p}=(0,eq.y)({...e,ref:n,selectionManager:t,keyboardDelegate:c});return{listProps:p}}({...e,ref:a,selectionManager:t.selectionManager,collection:t.collection,disabledKeys:t.disabledKeys,linkBehavior:o}),{focusWithinProps:r}=(0,eR.R)({onFocusWithin:e.onFocus,onBlurWithin:e.onBlur,onFocusWithinChange:e.onFocusChange}),s=(0,ep.Bi)(e.id);eK.set(t,{id:s,shouldUseVirtualFocus:e.shouldUseVirtualFocus,shouldSelectOnPressUp:e.shouldSelectOnPressUp,shouldFocusOnHover:e.shouldFocusOnHover,isVirtualized:e.isVirtualized,onAction:e.onAction,linkBehavior:o});let{labelProps:c,fieldProps:p}=(0,eW.M)({...e,id:s,labelElementType:"span"});return{labelProps:c,listBoxProps:(0,x.v)(i,r,"multiple"===t.selectionManager.selectionMode?{"aria-multiselectable":"true"}:{},{role:"listbox",...(0,x.v)(p,l)})}}({...e,onAction:c},R,A),T=(0,u.useMemo)(()=>eH({className:b}),[b]),B=(0,g.$)(null==C?void 0:C.base,b);return{Component:O,state:R,variant:r,color:s,slots:T,classNames:C,topContent:w,bottomContent:k,emptyContent:E,hideEmptyContent:D,shouldHighlightOnFocus:S,hideSelectedIcon:z,disableAnimation:v,className:b,itemClasses:h,getBaseProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:A,"data-slot":"base",className:T.base({class:B}),...(0,d.$)(P,{enabled:M}),...e}},getListProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{"data-slot":"list",className:T.list({class:null==C?void 0:C.list}),...I,...e}},getEmptyContentProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{"data-slot":"empty-content",children:E,className:T.emptyContent({class:null==C?void 0:C.emptyContent}),...e}}}}({...i,ref:t}),{Component:l,state:r,color:s,variant:c,itemClasses:m,getBaseProps:f,topContent:v,bottomContent:h,hideEmptyContent:b,hideSelectedIcon:w,shouldHighlightOnFocus:k,disableAnimation:E,getEmptyContentProps:z,getListProps:D}=n;if(a)return(0,e_.jsx)(tf,{...e,...n});let S=(0,e_.jsxs)(l,{...D(),children:[!r.collection.size&&!b&&(0,e_.jsx)("li",{children:(0,e_.jsx)("div",{...z()})}),[...r.collection].map(e=>{var t;let a={color:s,item:e,state:r,variant:c,disableAnimation:E,hideSelectedIcon:w,...e.props};if("section"===e.type)return(0,e_.jsx)(e2,{...a,itemClasses:m},e.key);let i=(0,e_.jsx)(e1,{...a,classNames:(0,x.v)(m,null==(t=e.props)?void 0:t.classNames),shouldHighlightOnFocus:k},e.key);return e.wrapper&&(i=e.wrapper(i)),i})]});return(0,e_.jsxs)("div",{...f(),children:[v,S,h]})}),tv=a(98179),th=a(32047),tx=a(87418);function tb(e,t){if(e.button>0)return!1;if(e.target){let t=e.target.ownerDocument;if(!t||!t.documentElement.contains(e.target)||e.target.closest("[data-react-aria-top-layer]"))return!1}return t.current&&!t.current.contains(e.target)}let ty=[];function tw(e,t=-1/0,a=1/0){return Math.min(Math.max(e,t),a)}let tk={top:"top",bottom:"top",left:"left",right:"left"},tE={top:"bottom",bottom:"top",left:"right",right:"left"},tz={top:"left",left:"top"},tD={top:"height",left:"width"},tj={width:"totalWidth",height:"totalHeight"},tS={},tC="undefined"!=typeof document?window.visualViewport:null;function tP(e){var t,a,i,n,o;let l=0,r=0,s=0,c=0,p=0,d=0,u={},m=(null!=(t=null==tC?void 0:tC.scale)?t:1)>1;if("BODY"===e.tagName){let t=document.documentElement;s=t.clientWidth,c=t.clientHeight,l=null!=(a=null==tC?void 0:tC.width)?a:s,r=null!=(i=null==tC?void 0:tC.height)?i:c,u.top=t.scrollTop||e.scrollTop,u.left=t.scrollLeft||e.scrollLeft,tC&&(p=tC.offsetTop,d=tC.offsetLeft)}else({width:l,height:r,top:p,left:d}=tR(e)),u.top=e.scrollTop,u.left=e.scrollLeft,s=l,c=r;return(0,eX.Tc)()&&("BODY"===e.tagName||"HTML"===e.tagName)&&m&&(u.top=0,u.left=0,p=null!=(n=null==tC?void 0:tC.pageTop)?n:0,d=null!=(o=null==tC?void 0:tC.pageLeft)?o:0),{width:l,height:r,totalWidth:s,totalHeight:c,scroll:u,top:p,left:d}}function tO(e,t,a,i,n,o,l){var r;let s=null!=(r=n.scroll[e])?r:0,c=i[tD[e]],p=i.scroll[tk[e]]+o,d=c+i.scroll[tk[e]]-o,u=t-s+l[e]-i[tk[e]],m=t-s+a+l[e]-i[tk[e]];return u<p?p-u:m>d?Math.max(d-m,p-u):0}function tM(e){if(tS[e])return tS[e];let[t,a]=e.split(" "),i=tk[t]||"right",n=tz[i];tk[a]||(a="center");let o=tD[i],l=tD[n];return tS[e]={placement:t,crossPlacement:a,axis:i,crossAxis:n,size:o,crossSize:l},tS[e]}function tA(e,t,a,i,n,o,l,r,s,c){var p,d,u,m,f;let{placement:g,crossPlacement:v,axis:h,crossAxis:x,size:b,crossSize:y}=i,w={};w[x]=null!=(p=e[x])?p:0,"center"===v?w[x]+=((null!=(d=e[y])?d:0)-(null!=(u=a[y])?u:0))/2:v!==x&&(w[x]+=(null!=(m=e[y])?m:0)-(null!=(f=a[y])?f:0)),w[x]+=o;let k=e[x]-a[y]+s+c,E=e[x]+e[y]-s-c;if(w[x]=tw(w[x],k,E),g===h){let a=r?l[b]:t[tj[b]];w[tE[h]]=Math.floor(a-e[h]+n)}else w[h]=Math.floor(e[h]+e[b]+n);return w}function tF(e,t,a,i,n,o){var l,r,s;let{placement:c,axis:p,size:d}=o;return c===p?Math.max(0,a[p]-e[p]-(null!=(l=e.scroll[p])?l:0)+t[p]-(null!=(r=i[p])?r:0)-i[tE[p]]-n):Math.max(0,e[d]+e[p]+e.scroll[p]-t[p]-a[p]-a[d]-(null!=(s=i[p])?s:0)-i[tE[p]]-n)}function tR(e){let{top:t,left:a,width:i,height:n}=e.getBoundingClientRect(),{scrollTop:o,scrollLeft:l,clientTop:r,clientLeft:s}=document.documentElement;return{top:t+o-r,left:a+l-s,width:i,height:n}}function tI(e,t){let a,i=window.getComputedStyle(e);if("fixed"===i.position){let{top:t,left:i,width:n,height:o}=e.getBoundingClientRect();a={top:t,left:i,width:n,height:o}}else{a=tR(e);let i=tR(t),n=window.getComputedStyle(t);i.top+=(parseInt(n.borderTopWidth,10)||0)-t.scrollTop,i.left+=(parseInt(n.borderLeftWidth,10)||0)-t.scrollLeft,a.top-=i.top,a.left-=i.left}return a.top-=parseInt(i.marginTop,10)||0,a.left-=parseInt(i.marginLeft,10)||0,a}function tT(e){let t=window.getComputedStyle(e);return"none"!==t.transform||/transform|perspective/.test(t.willChange)||"none"!==t.filter||"paint"===t.contain||"backdropFilter"in t&&"none"!==t.backdropFilter||"WebkitBackdropFilter"in t&&"none"!==t.WebkitBackdropFilter}var tB=a(33205);function tN(e){let{ref:t,box:a,onResize:i}=e;(0,u.useEffect)(()=>{let e=null==t?void 0:t.current;if(e)if(void 0===window.ResizeObserver)return window.addEventListener("resize",i,!1),()=>{window.removeEventListener("resize",i,!1)};else{let t=new window.ResizeObserver(e=>{e.length&&i()});return t.observe(e,{box:a}),()=>{e&&t.unobserve(e)}}},[i,t,a])}let t_="undefined"!=typeof document?window.visualViewport:null;var tL=e=>{let t={top:{originY:1},bottom:{originY:0},left:{originX:1},right:{originX:0},"top-start":{originX:0,originY:1},"top-end":{originX:1,originY:1},"bottom-start":{originX:0,originY:0},"bottom-end":{originX:1,originY:0},"right-start":{originX:0,originY:0},"right-end":{originX:0,originY:1},"left-start":{originX:1,originY:0},"left-end":{originX:1,originY:1}};return(null==t?void 0:t[e])||{}},tK=e=>({top:"top",bottom:"bottom",left:"left",right:"right","top-start":"top start","top-end":"top end","bottom-start":"bottom start","bottom-end":"bottom end","left-start":"left top","left-end":"left bottom","right-start":"right top","right-end":"right bottom"})[e],tW=(e,t)=>{if(t.includes("-")){let[a]=t.split("-");if(a.includes(e))return!1}return!0},tq=(e,t)=>{if(t.includes("-")){let[,a]=t.split("-");return"".concat(e,"-").concat(a)}return e},tH=new WeakMap,t$=[];function tV(e,t){let a=e;for((0,eE.o)(a,t)&&(a=a.parentElement);a&&!(0,eE.o)(a,t);)a=a.parentElement;return a||document.scrollingElement||document.documentElement}let tU="undefined"!=typeof document&&window.visualViewport,tY=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]),tX=0;function tG(e,t,a){let i=e.style[t];return e.style[t]=a,()=>{e.style[t]=i}}function tJ(e,t,a,i){return e.addEventListener(t,a,i),()=>{e.removeEventListener(t,a,i)}}function tZ(e){let t=document.scrollingElement||document.documentElement,a=e;for(;a&&a!==t;){let e=tV(a);if(e!==document.documentElement&&e!==document.body&&e!==a){let t=e.getBoundingClientRect().top,i=a.getBoundingClientRect().top;i>t+a.clientHeight&&(e.scrollTop+=i-t)}a=e.parentElement}}function tQ(e){return e instanceof HTMLInputElement&&!tY.has(e.type)||e instanceof HTMLTextAreaElement||e instanceof HTMLElement&&e.isContentEditable}let t0=new WeakMap,t1=[];var t4=a(70418),t3=(0,r.tv)({slots:{base:["z-0","relative","bg-transparent","before:content-['']","before:hidden","before:z-[-1]","before:absolute","before:rotate-45","before:w-2.5","before:h-2.5","before:rounded-sm","data-[arrow=true]:before:block","data-[placement=top]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=top]:before:left-1/2","data-[placement=top]:before:-translate-x-1/2","data-[placement=top-start]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=top-start]:before:left-3","data-[placement=top-end]:before:-bottom-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=top-end]:before:right-3","data-[placement=bottom]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=bottom]:before:left-1/2","data-[placement=bottom]:before:-translate-x-1/2","data-[placement=bottom-start]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=bottom-start]:before:left-3","data-[placement=bottom-end]:before:-top-[calc(theme(spacing.5)/4_-_1.5px)]","data-[placement=bottom-end]:before:right-3","data-[placement=left]:before:-right-[calc(theme(spacing.5)/4_-_2px)]","data-[placement=left]:before:top-1/2","data-[placement=left]:before:-translate-y-1/2","data-[placement=left-start]:before:-right-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=left-start]:before:top-1/4","data-[placement=left-end]:before:-right-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=left-end]:before:bottom-1/4","data-[placement=right]:before:-left-[calc(theme(spacing.5)/4_-_2px)]","data-[placement=right]:before:top-1/2","data-[placement=right]:before:-translate-y-1/2","data-[placement=right-start]:before:-left-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=right-start]:before:top-1/4","data-[placement=right-end]:before:-left-[calc(theme(spacing.5)/4_-_3px)]","data-[placement=right-end]:before:bottom-1/4",...s.zb],content:["z-10","px-2.5","py-1","w-full","inline-flex","flex-col","items-center","justify-center","box-border","subpixel-antialiased","outline-none","box-border"],trigger:["z-10"],backdrop:["hidden"],arrow:[]},variants:{size:{sm:{content:"text-tiny"},md:{content:"text-small"},lg:{content:"text-medium"}},color:{default:{base:"before:bg-content1 before:shadow-small",content:"bg-content1"},foreground:{base:"before:bg-foreground",content:t4.k.solid.foreground},primary:{base:"before:bg-primary",content:t4.k.solid.primary},secondary:{base:"before:bg-secondary",content:t4.k.solid.secondary},success:{base:"before:bg-success",content:t4.k.solid.success},warning:{base:"before:bg-warning",content:t4.k.solid.warning},danger:{base:"before:bg-danger",content:t4.k.solid.danger}},radius:{none:{content:"rounded-none"},sm:{content:"rounded-small"},md:{content:"rounded-medium"},lg:{content:"rounded-large"},full:{content:"rounded-full"}},shadow:{sm:{content:"shadow-small"},md:{content:"shadow-medium"},lg:{content:"shadow-large"}},backdrop:{transparent:{},opaque:{backdrop:"bg-overlay/50 backdrop-opacity-disabled"},blur:{backdrop:"backdrop-blur-sm backdrop-saturate-150 bg-overlay/30"}},triggerScaleOnOpen:{true:{trigger:["aria-expanded:scale-[0.97]","aria-expanded:opacity-70","subpixel-antialiased"]},false:{}},disableAnimation:{true:{base:"animate-none"}},isTriggerDisabled:{true:{trigger:"opacity-disabled pointer-events-none"},false:{}}},defaultVariants:{color:"default",radius:"lg",size:"md",shadow:"md",backdrop:"transparent",triggerScaleOnOpen:!0},compoundVariants:[{backdrop:["opaque","blur"],class:{backdrop:"block w-full h-full fixed inset-0 -z-30"}}]});let t2=(0,u.createContext)({});var t5=a(22706);function t6({children:e}){let t=(0,u.useMemo)(()=>({register:()=>{}}),[]);return u.createElement(t5.F.Provider,{value:t},e)}let t7=u.createContext(null);function t8(e){var t;let a=(0,e0.wR)(),{portalContainer:i=a?null:document.body,isExiting:n}=e,[o,l]=(0,u.useState)(!1),r=(0,u.useMemo)(()=>({contain:o,setContain:l}),[o,l]),{getContainer:s}=null!=(t=(0,u.useContext)(t2))?t:{};if(!e.portalContainer&&s&&(i=s()),!i)return null;let c=e.children;return e.disableFocusManagement||(c=u.createElement(tv.n1,{restoreFocus:!0,contain:o&&!n},c)),c=u.createElement(t7.Provider,{value:r},u.createElement(t6,null,c)),e5.createPortal(c,i)}var t9={},ae={},at={},aa={},ai={},an={},ao={},al={},ar={},as={},ac={},ap={},ad={},au={},am={},af={},ag={},av={},ah={},ax={},ab={},ay={},aw={},ak={},aE={},az={},aD={},aj={},aS={},aC={},aP={},aO={},aM={},aA={},aF={};aF={"ar-AE":{dismiss:`\u{62A}\u{62C}\u{627}\u{647}\u{644}`},"bg-BG":{dismiss:`\u{41E}\u{442}\u{445}\u{432}\u{44A}\u{440}\u{43B}\u{44F}\u{43D}\u{435}`},"cs-CZ":{dismiss:"Odstranit"},"da-DK":{dismiss:"Luk"},"de-DE":{dismiss:`Schlie\xdfen`},"el-GR":{dismiss:`\u{391}\u{3C0}\u{3CC}\u{3C1}\u{3C1}\u{3B9}\u{3C8}\u{3B7}`},"en-US":{dismiss:"Dismiss"},"es-ES":{dismiss:"Descartar"},"et-EE":{dismiss:`L\xf5peta`},"fi-FI":{dismiss:`Hylk\xe4\xe4`},"fr-FR":{dismiss:"Rejeter"},"he-IL":{dismiss:`\u{5D4}\u{5EA}\u{5E2}\u{5DC}\u{5DD}`},"hr-HR":{dismiss:"Odbaci"},"hu-HU":{dismiss:`Elutas\xedt\xe1s`},"it-IT":{dismiss:"Ignora"},"ja-JP":{dismiss:`\u{9589}\u{3058}\u{308B}`},"ko-KR":{dismiss:`\u{BB34}\u{C2DC}`},"lt-LT":{dismiss:"Atmesti"},"lv-LV":{dismiss:`Ner\u{101}d\u{12B}t`},"nb-NO":{dismiss:"Lukk"},"nl-NL":{dismiss:"Negeren"},"pl-PL":{dismiss:"Zignoruj"},"pt-BR":{dismiss:"Descartar"},"pt-PT":{dismiss:"Dispensar"},"ro-RO":{dismiss:"Revocare"},"ru-RU":{dismiss:`\u{41F}\u{440}\u{43E}\u{43F}\u{443}\u{441}\u{442}\u{438}\u{442}\u{44C}`},"sk-SK":{dismiss:`Zru\u{161}i\u{165}`},"sl-SI":{dismiss:"Opusti"},"sr-SP":{dismiss:"Odbaci"},"sv-SE":{dismiss:"Avvisa"},"tr-TR":{dismiss:"Kapat"},"uk-UA":{dismiss:`\u{421}\u{43A}\u{430}\u{441}\u{443}\u{432}\u{430}\u{442}\u{438}`},"zh-CN":{dismiss:`\u{53D6}\u{6D88}`},"zh-TW":{dismiss:`\u{95DC}\u{9589}`}};var aR=a(66933);function aI(e){var t;let{onDismiss:a,...i}=e,n=ex((t=aF)&&t.__esModule?t.default:t,"@react-aria/overlays"),o=(0,aR.b)(i,n.format("dismiss"));return u.createElement(eB,null,u.createElement("button",{...o,tabIndex:-1,onClick:()=>{a&&a()},style:{width:1,height:1}}))}var aT=a(51251),aB=a(14356),aN={ease:[.36,.66,.4,1],easeIn:[.4,0,1,1],easeOut:[0,0,.2,1]};aN.easeOut,aN.easeIn;var a_={scaleSpring:{enter:{transform:"scale(1)",opacity:1,transition:{type:"spring",bounce:0,duration:.2}},exit:{transform:"scale(0.85)",opacity:0,transition:{type:"easeOut",duration:.15}}},scaleSpringOpacity:{initial:{opacity:0,transform:"scale(0.8)"},enter:{opacity:1,transform:"scale(1)",transition:{type:"spring",bounce:0,duration:.3}},exit:{opacity:0,transform:"scale(0.96)",transition:{type:"easeOut",bounce:0,duration:.15}}},scale:{enter:{scale:1},exit:{scale:.95}},scaleFadeIn:{enter:{transform:"scale(1)",opacity:1,transition:{duration:.25,ease:aN.easeIn}},exit:{transform:"scale(0.95)",opacity:0,transition:{duration:.2,ease:aN.easeOut}}},scaleInOut:{enter:{transform:"scale(1)",opacity:1,transition:{duration:.4,ease:aN.ease}},exit:{transform:"scale(1.03)",opacity:0,transition:{duration:.3,ease:aN.ease}}},fade:{enter:{opacity:1,transition:{duration:.4,ease:aN.ease}},exit:{opacity:0,transition:{duration:.3,ease:aN.ease}}},collapse:{enter:{opacity:1,height:"auto",transition:{height:{type:"spring",bounce:0,duration:.3},opacity:{easings:"ease",duration:.4}}},exit:{opacity:0,height:0,transition:{easings:"ease",duration:.3}}}},aL=a(30502),aK=()=>a.e(843).then(a.bind(a,49224)).then(e=>e.default),aW=(0,l.Rf)((e,t)=>{let{children:a,motionProps:i,placement:n,disableAnimation:o,style:l={},transformOrigin:r={},...s}=e,c=l;return void 0!==r.originX||void 0!==r.originY?c={...c,transformOrigin:r}:n&&(c={...c,...tL("center"===n?"top":n)}),o?(0,e_.jsx)("div",{...s,ref:t,children:a}):(0,e_.jsx)(aT.F,{features:aK,children:(0,e_.jsx)(aB.m.div,{ref:t,animate:"enter",exit:"exit",initial:"initial",style:c,variants:a_.scaleSpringOpacity,...(0,x.v)(s,i),children:a})})});aW.displayName="NextUI.FreeSoloPopoverWrapper";var aq=(0,l.Rf)((e,t)=>{let{children:a,transformOrigin:n,disableDialogFocus:r=!1,...s}=e,{Component:c,state:d,placement:m,backdrop:b,portalContainer:y,disableAnimation:w,motionProps:E,isNonModal:D,getPopoverProps:j,getBackdropProps:S,getDialogProps:C,getContentProps:P}=function(e){var t,a,n;let r=(0,o.o)(),[s,c]=(0,l.rE)(e,t3.variantKeys),{as:d,ref:m,children:b,state:y,triggerRef:w,scrollRef:E,defaultOpen:D,onOpenChange:j,isOpen:S,isNonModal:C=!0,shouldFlip:P=!0,containerPadding:O=12,shouldBlockScroll:M=!1,isDismissable:A=!0,shouldCloseOnBlur:F,portalContainer:R,updatePositionDeps:I,dialogProps:T,placement:B="top",triggerType:N="dialog",showArrow:_=!1,offset:L=7,crossOffset:K=0,boundaryElement:W,isKeyboardDismissDisabled:q,shouldCloseOnInteractOutside:H,shouldCloseOnScroll:$,motionProps:V,className:U,classNames:Y,onClose:X,...G}=s,J=(0,p.zD)(m),Z=(0,u.useRef)(null),Q=(0,u.useRef)(!1),ee=w||Z,et=null!=(a=null!=(t=e.disableAnimation)?t:null==r?void 0:r.disableAnimation)&&a,ea=k({isOpen:S,defaultOpen:D,onOpenChange:e=>{null==j||j(e),e||null==X||X()}}),ei=y||ea,{popoverProps:en,underlayProps:eo,placement:el}=function(e,t){let{triggerRef:a,popoverRef:i,showArrow:n,offset:o=7,crossOffset:l=0,scrollRef:r,shouldFlip:s,boundaryElement:c,isDismissable:p=!0,shouldCloseOnBlur:d=!0,shouldCloseOnScroll:m=!0,placement:f="top",containerPadding:g,shouldCloseOnInteractOutside:v,isNonModal:h,isKeyboardDismissDisabled:b,updatePositionDeps:y=[],...w}=e,k=null==h||h,{overlayProps:E,underlayProps:D}=function(e,t){let{onClose:a,shouldCloseOnBlur:i,isOpen:n,isDismissable:o=!1,isKeyboardDismissDisabled:l=!1,shouldCloseOnInteractOutside:r}=e;(0,u.useEffect)(()=>(n&&ty.push(t),()=>{let e=ty.indexOf(t);e>=0&&ty.splice(e,1)}),[n,t]);let s=()=>{ty[ty.length-1]===t&&a&&a()};!function(e){let{ref:t,onInteractOutside:a,isDisabled:i,onInteractOutsideStart:n}=e,o=(0,u.useRef)({isPointerDown:!1,ignoreEmulatedMouseEvents:!1}),l=(0,th.J)(e=>{a&&tb(e,t)&&(n&&n(e),o.current.isPointerDown=!0)}),r=(0,th.J)(e=>{a&&a(e)});(0,u.useEffect)(()=>{let e=o.current;if(i)return;let a=t.current,n=(0,tx.T)(a);if("undefined"!=typeof PointerEvent){let a=a=>{e.isPointerDown&&tb(a,t)&&r(a),e.isPointerDown=!1};return n.addEventListener("pointerdown",l,!0),n.addEventListener("pointerup",a,!0),()=>{n.removeEventListener("pointerdown",l,!0),n.removeEventListener("pointerup",a,!0)}}{let a=a=>{e.ignoreEmulatedMouseEvents?e.ignoreEmulatedMouseEvents=!1:e.isPointerDown&&tb(a,t)&&r(a),e.isPointerDown=!1},i=a=>{e.ignoreEmulatedMouseEvents=!0,e.isPointerDown&&tb(a,t)&&r(a),e.isPointerDown=!1};return n.addEventListener("mousedown",l,!0),n.addEventListener("mouseup",a,!0),n.addEventListener("touchstart",l,!0),n.addEventListener("touchend",i,!0),()=>{n.removeEventListener("mousedown",l,!0),n.removeEventListener("mouseup",a,!0),n.removeEventListener("touchstart",l,!0),n.removeEventListener("touchend",i,!0)}}},[t,i,l,r])}({ref:t,onInteractOutside:o&&n?e=>{(!r||r(e.target))&&(ty[ty.length-1]===t&&(e.stopPropagation(),e.preventDefault()),s())}:void 0,onInteractOutsideStart:e=>{(!r||r(e.target))&&ty[ty.length-1]===t&&(e.stopPropagation(),e.preventDefault())}});let{focusWithinProps:c}=(0,eR.R)({isDisabled:!i,onBlurWithin:e=>{!(!e.relatedTarget||(0,tv.Pu)(e.relatedTarget))&&(!r||r(e.relatedTarget))&&(null==a||a())}});return{overlayProps:{onKeyDown:e=>{"Escape"!==e.key||l||e.nativeEvent.isComposing||(e.stopPropagation(),e.preventDefault(),s())},...c},underlayProps:{onPointerDown:e=>{e.target===e.currentTarget&&e.preventDefault()}}}}({isOpen:t.isOpen,onClose:t.close,shouldCloseOnBlur:d,isDismissable:p,isKeyboardDismissDisabled:b,shouldCloseOnInteractOutside:v||(e=>eP(e,a,t))},i),{overlayProps:j,arrowProps:S,placement:C,updatePosition:P}=function(e){var t,a,i;let{direction:n}=(0,z.Y)(),{arrowSize:o=0,targetRef:l,overlayRef:r,scrollRef:s=r,placement:c="bottom",containerPadding:p=12,shouldFlip:d=!0,boundaryElement:m="undefined"!=typeof document?document.body:null,offset:f=0,crossOffset:g=0,shouldUpdatePosition:v=!0,isOpen:h=!0,onClose:x,maxHeight:b,arrowBoundaryOffset:y=0}=e,[w,k]=(0,u.useState)(null),E=[v,c,r.current,l.current,s.current,p,d,m,f,g,h,n,b,y,o],D=(0,u.useRef)(null==t_?void 0:t_.scale);(0,u.useEffect)(()=>{h&&(D.current=null==t_?void 0:t_.scale)},[h]);let j=(0,u.useCallback)(()=>{var e,t,a,i,u,x;if(!1===v||!h||!r.current||!l.current||!m||(null==t_?void 0:t_.scale)!==D.current)return;let w=null;if(s.current&&s.current.contains(document.activeElement)){let i=null==(e=document.activeElement)?void 0:e.getBoundingClientRect(),n=s.current.getBoundingClientRect();(w={type:"top",offset:(null!=(t=null==i?void 0:i.top)?t:0)-n.top}).offset>n.height/2&&(w.type="bottom",w.offset=(null!=(a=null==i?void 0:i.bottom)?a:0)-n.bottom)}let E=r.current;!b&&r.current&&(E.style.top="0px",E.style.bottom="",E.style.maxHeight=(null!=(u=null==(i=window.visualViewport)?void 0:i.height)?u:window.innerHeight)+"px");let z=function(e){var t,a,i,n;let o,{placement:l,targetNode:r,overlayNode:s,scrollNode:c,padding:p,shouldFlip:d,boundaryElement:u,offset:m,crossOffset:f,maxHeight:g,arrowSize:v=0,arrowBoundaryOffset:h=0}=e,x=s instanceof HTMLElement?function(e){let t=e.offsetParent;if(t&&t===document.body&&"static"===window.getComputedStyle(t).position&&!tT(t)&&(t=document.documentElement),null==t)for(t=e.parentElement;t&&!tT(t);)t=t.parentElement;return t||document.documentElement}(s):document.documentElement,b=x===document.documentElement,y=window.getComputedStyle(x).position,w=b?tR(r):tI(r,x);if(!b){let{marginTop:e,marginLeft:t}=window.getComputedStyle(r);w.top+=parseInt(e,10)||0,w.left+=parseInt(t,10)||0}let k=tR(s),E={top:parseInt((o=window.getComputedStyle(s)).marginTop,10)||0,bottom:parseInt(o.marginBottom,10)||0,left:parseInt(o.marginLeft,10)||0,right:parseInt(o.marginRight,10)||0};k.width+=(null!=(t=E.left)?t:0)+(null!=(a=E.right)?a:0),k.height+=(null!=(i=E.top)?i:0)+(null!=(n=E.bottom)?n:0);let z={top:c.scrollTop,left:c.scrollLeft,width:c.scrollWidth,height:c.scrollHeight},D=tP(u),j=tP(x),S="BODY"===u.tagName?tR(x):tI(x,u);return"HTML"===x.tagName&&"BODY"===u.tagName&&(j.scroll.top=0,j.scroll.left=0),function(e,t,a,i,n,o,l,r,s,c,p,d,u,m,f,g){var v,h,x,b;let y=tM(e),{size:w,crossAxis:k,crossSize:E,placement:z,crossPlacement:D}=y,j=tA(t,r,a,y,p,d,c,u,f,g),S=p,C=tF(r,c,t,n,o+p,y);if(l&&i[w]>C){let e=tM(`${tE[z]} ${D}`),i=tA(t,r,a,e,p,d,c,u,f,g);tF(r,c,t,n,o+p,e)>C&&(y=e,j=i,S=p)}let P="bottom";"top"===y.axis?"top"===y.placement?P="top":"bottom"===y.placement&&(P="bottom"):"top"===y.crossAxis&&("top"===y.crossPlacement?P="bottom":"bottom"===y.crossPlacement&&(P="top"));let O=tO(k,j[k],a[E],r,s,o,c);j[k]+=O;let M=function(e,t,a,i,n,o,l,r){var s,c,p,d,u,m,f;let g=i?a.height:t[tj.height],v=null!=e.top?a.top+e.top:a.top+(g-(null!=(s=e.bottom)?s:0)-l),h="top"!==r?Math.max(0,t.height+t.top+(null!=(c=t.scroll.top)?c:0)-v-((null!=(p=n.top)?p:0)+(null!=(d=n.bottom)?d:0)+o)):Math.max(0,v+l-(t.top+(null!=(u=t.scroll.top)?u:0))-((null!=(m=n.top)?m:0)+(null!=(f=n.bottom)?f:0)+o));return Math.min(t.height-2*o,h)}(j,r,c,u,n,o,a.height,P);m&&m<M&&(M=m),a.height=Math.min(a.height,M),O=tO(k,(j=tA(t,r,a,y,S,d,c,u,f,g))[k],a[E],r,s,o,c),j[k]+=O;let A={},F=t[k]+.5*t[E]-j[k]-n[tk[k]],R=f/2+g,I="left"===tk[k]?(null!=(v=n.left)?v:0)+(null!=(h=n.right)?h:0):(null!=(x=n.top)?x:0)+(null!=(b=n.bottom)?b:0),T=a[E]-I-f/2-g,B=tw(F,t[k]+f/2-(j[k]+n[tk[k]]),t[k]+t[E]-f/2-(j[k]+n[tk[k]]));return A[k]=tw(B,R,T),{position:j,maxHeight:M,arrowOffsetLeft:A.left,arrowOffsetTop:A.top,placement:y.placement}}(l,w,k,z,E,p,d,D,j,S,m,f,!!y&&"static"!==y,g,v,h)}({placement:(x=c,"rtl"===n?x.replace("start","right").replace("end","left"):x.replace("start","left").replace("end","right")),overlayNode:r.current,targetNode:l.current,scrollNode:s.current||r.current,padding:p,shouldFlip:d,boundaryElement:m,offset:f,crossOffset:g,maxHeight:b,arrowSize:o,arrowBoundaryOffset:y});if(z.position){if(E.style.top="",E.style.bottom="",E.style.left="",E.style.right="",Object.keys(z.position).forEach(e=>E.style[e]=z.position[e]+"px"),E.style.maxHeight=null!=z.maxHeight?z.maxHeight+"px":"",w&&document.activeElement&&s.current){let e=document.activeElement.getBoundingClientRect(),t=s.current.getBoundingClientRect(),a=e[w.type]-t[w.type];s.current.scrollTop+=a-w.offset}k(z)}},E);(0,tB.N)(j,E),i=j,(0,tB.N)(()=>(window.addEventListener("resize",i,!1),()=>{window.removeEventListener("resize",i,!1)}),[i]),tN({ref:r,onResize:j}),tN({ref:l,onResize:j});let S=(0,u.useRef)(!1);(0,tB.N)(()=>{let e,t=()=>{S.current=!0,clearTimeout(e),e=setTimeout(()=>{S.current=!1},500),j()},a=()=>{S.current&&t()};return null==t_||t_.addEventListener("resize",t),null==t_||t_.addEventListener("scroll",a),()=>{null==t_||t_.removeEventListener("resize",t),null==t_||t_.removeEventListener("scroll",a)}},[j]);let C=(0,u.useCallback)(()=>{S.current||null==x||x()},[x,S]);return!function(e){let{triggerRef:t,isOpen:a,onClose:i}=e;(0,u.useEffect)(()=>{if(!a||null===i)return;let e=e=>{let a=e.target;if(!t.current||a instanceof Node&&!a.contains(t.current)||e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement)return;let n=i||ey.get(t.current);n&&n()};return window.addEventListener("scroll",e,!0),()=>{window.removeEventListener("scroll",e,!0)}},[a,i,t])}({triggerRef:l,isOpen:h,onClose:x&&C}),{overlayProps:{style:{position:"absolute",zIndex:1e5,...null==w?void 0:w.position,maxHeight:null!=(t=null==w?void 0:w.maxHeight)?t:"100vh"}},placement:null!=(a=null==w?void 0:w.placement)?a:null,arrowProps:{"aria-hidden":"true",role:"presentation",style:{left:null==w?void 0:w.arrowOffsetLeft,top:null==w?void 0:w.arrowOffsetTop}},updatePosition:j}}({...w,shouldFlip:s,crossOffset:l,targetRef:a,overlayRef:i,isOpen:t.isOpen,scrollRef:r,boundaryElement:c,containerPadding:g,placement:tK(f),offset:n?o+3:o,onClose:k&&m?t.close:()=>{}});return(0,eC.U)(()=>{y.length&&P()},y),(0,u.useEffect)(()=>{if(t.isOpen&&!k&&i.current)return function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document.body,a=new Set(e),i=new Set,n=e=>{for(let t of e.querySelectorAll("[data-live-announcer], [data-react-aria-top-layer]"))a.add(t);let t=e=>{if(a.has(e)||e.parentElement&&i.has(e.parentElement)&&"row"!==e.parentElement.getAttribute("role"))return NodeFilter.FILTER_REJECT;for(let t of a)if(e.contains(t))return NodeFilter.FILTER_SKIP;return NodeFilter.FILTER_ACCEPT},n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:t}),l=t(e);if(l===NodeFilter.FILTER_ACCEPT&&o(e),l!==NodeFilter.FILTER_REJECT){let e=n.nextNode();for(;null!=e;)o(e),e=n.nextNode()}},o=e=>{var t;let a=null!=(t=tH.get(e))?t:0;("true"!==e.getAttribute("aria-hidden")||0!==a)&&(0===a&&e.setAttribute("aria-hidden","true"),i.add(e),tH.set(e,a+1))};t$.length&&t$[t$.length-1].disconnect(),n(t);let l=new MutationObserver(e=>{for(let t of e)if("childList"===t.type&&0!==t.addedNodes.length&&![...a,...i].some(e=>e.contains(t.target))){for(let e of t.removedNodes)e instanceof Element&&(a.delete(e),i.delete(e));for(let e of t.addedNodes)(e instanceof HTMLElement||e instanceof SVGElement)&&("true"===e.dataset.liveAnnouncer||"true"===e.dataset.reactAriaTopLayer)?a.add(e):e instanceof Element&&n(e)}});l.observe(t,{childList:!0,subtree:!0});let r={observe(){l.observe(t,{childList:!0,subtree:!0})},disconnect(){l.disconnect()}};return t$.push(r),()=>{for(let e of(l.disconnect(),i)){let t=tH.get(e);null!=t&&(1===t?(e.removeAttribute("aria-hidden"),tH.delete(e)):tH.set(e,t-1))}r===t$[t$.length-1]?(t$.pop(),t$.length&&t$[t$.length-1].observe()):t$.splice(t$.indexOf(r),1)}}([i.current])},[k,t.isOpen,i]),{popoverProps:(0,x.v)(E,j),arrowProps:S,underlayProps:D,placement:C}}({triggerRef:ee,isNonModal:C,popoverRef:J,placement:B,offset:L,scrollRef:E,isDismissable:A,shouldCloseOnBlur:F,boundaryElement:W,crossOffset:K,shouldFlip:P,containerPadding:O,updatePositionDeps:I,isKeyboardDismissDisabled:q,shouldCloseOnScroll:$,shouldCloseOnInteractOutside:H},ei),er=(0,u.useMemo)(()=>el?tW(el,B)?el:B:null,[el,B]),{triggerProps:es}=ew({type:N},ei,ee),{isFocusVisible:ec,isFocused:ep,focusProps:ed}=(0,f.o)(),eu=(0,u.useMemo)(()=>t3({...c}),[(0,v.t6)(c)]),em=(0,g.$)(null==Y?void 0:Y.base,U);!function(e={}){let{isDisabled:t}=e;(0,tB.N)(()=>{if(!t){let e,t,a,n,o;return 1==++tX&&(i=(0,eX.un)()?(a=null,n=()=>{if(a)return;let e=window.pageXOffset,t=window.pageYOffset;a=(0,eS.c)(tJ(window,"scroll",()=>{window.scrollTo(0,0)}),tG(document.documentElement,"paddingRight",`${window.innerWidth-document.documentElement.clientWidth}px`),tG(document.documentElement,"overflow","hidden"),tG(document.body,"marginTop",`-${t}px`),()=>{window.scrollTo(e,t)}),window.scrollTo(0,0)},o=(0,eS.c)(tJ(document,"touchstart",a=>{((e=tV(a.target,!0))!==document.documentElement||e!==document.body)&&e instanceof HTMLElement&&"auto"===window.getComputedStyle(e).overscrollBehavior&&(t=tG(e,"overscrollBehavior","contain"))},{passive:!1,capture:!0}),tJ(document,"touchmove",t=>{if(!e||e===document.documentElement||e===document.body)return void t.preventDefault();e.scrollHeight===e.clientHeight&&e.scrollWidth===e.clientWidth&&t.preventDefault()},{passive:!1,capture:!0}),tJ(document,"touchend",e=>{let a=e.target;tQ(a)&&a!==document.activeElement&&(e.preventDefault(),n(),a.style.transform="translateY(-2000px)",a.focus(),requestAnimationFrame(()=>{a.style.transform=""})),t&&t()},{passive:!1,capture:!0}),tJ(document,"focus",e=>{let t=e.target;tQ(t)&&(n(),t.style.transform="translateY(-2000px)",requestAnimationFrame(()=>{t.style.transform="",tU&&(tU.height<window.innerHeight?requestAnimationFrame(()=>{tZ(t)}):tU.addEventListener("resize",()=>tZ(t),{once:!0}))}))},!0)),()=>{null==t||t(),null==a||a(),o()}):(0,eS.c)(tG(document.documentElement,"paddingRight",`${window.innerWidth-document.documentElement.clientWidth}px`),tG(document.documentElement,"overflow","hidden"))),()=>{0==--tX&&i()}}},[t])}({isDisabled:!(M&&ei.isOpen)});let ef=(0,u.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{"data-slot":"content","data-open":(0,h.sE)(ei.isOpen),"data-arrow":(0,h.sE)(_),"data-placement":el?tq(el,B):void 0,className:eu.content({class:(0,g.$)(null==Y?void 0:Y.content,e.className)})}},[eu,ei.isOpen,_,er,B,Y,el]),eg=(0,u.useCallback)(t=>{var a;let i;return"touch"===t.pointerType&&((null==e?void 0:e.backdrop)==="blur"||(null==e?void 0:e.backdrop)==="opaque")?i=setTimeout(()=>{Q.current=!0},100):Q.current=!0,null==(a=es.onPress)||a.call(es,t),()=>{clearTimeout(i)}},[null==es?void 0:es.onPress]),ev=(0,u.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,{isDisabled:a,...i}=e;return{"data-slot":"trigger",...(0,x.v)({"aria-haspopup":"dialog"},es,i),onPress:eg,isDisabled:a,className:eu.trigger({class:(0,g.$)(null==Y?void 0:Y.trigger,e.className),isTriggerDisabled:a}),ref:function(...e){return 1===e.length&&e[0]?e[0]:t=>{for(let a of e)"function"==typeof a?a(t):null!=a&&(a.current=t)}}(t,ee)}},[ei,es,eg,ee]),eh=(0,u.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{"data-slot":"backdrop",className:eu.backdrop({class:null==Y?void 0:Y.backdrop}),onClick:e=>{if(!Q.current)return void e.preventDefault();ei.close(),Q.current=!1},...eo,...e}},[eu,ei.isOpen,Y,eo]);return(0,u.useEffect)(()=>{if(ei.isOpen&&(null==J?void 0:J.current))return function(e,t=document.body){let a=new Set(e),i=new Set,n=e=>{for(let t of e.querySelectorAll("[data-live-announcer], [data-react-aria-top-layer]"))a.add(t);let t=e=>{if(a.has(e)||e.parentElement&&i.has(e.parentElement)&&"row"!==e.parentElement.getAttribute("role"))return NodeFilter.FILTER_REJECT;for(let t of a)if(e.contains(t))return NodeFilter.FILTER_SKIP;return NodeFilter.FILTER_ACCEPT},n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:t}),l=t(e);if(l===NodeFilter.FILTER_ACCEPT&&o(e),l!==NodeFilter.FILTER_REJECT){let e=n.nextNode();for(;null!=e;)o(e),e=n.nextNode()}},o=e=>{var t;let a=null!=(t=t0.get(e))?t:0;("true"!==e.getAttribute("aria-hidden")||0!==a)&&(0===a&&e.setAttribute("aria-hidden","true"),i.add(e),t0.set(e,a+1))};t1.length&&t1[t1.length-1].disconnect(),n(t);let l=new MutationObserver(e=>{for(let t of e)if("childList"===t.type&&0!==t.addedNodes.length&&![...a,...i].some(e=>e.contains(t.target))){for(let e of t.removedNodes)e instanceof Element&&(a.delete(e),i.delete(e));for(let e of t.addedNodes)(e instanceof HTMLElement||e instanceof SVGElement)&&("true"===e.dataset.liveAnnouncer||"true"===e.dataset.reactAriaTopLayer)?a.add(e):e instanceof Element&&n(e)}});l.observe(t,{childList:!0,subtree:!0});let r={observe(){l.observe(t,{childList:!0,subtree:!0})},disconnect(){l.disconnect()}};return t1.push(r),()=>{for(let e of(l.disconnect(),i)){let t=t0.get(e);null!=t&&(1===t?(e.removeAttribute("aria-hidden"),t0.delete(e)):t0.set(e,t-1))}r===t1[t1.length-1]?(t1.pop(),t1.length&&t1[t1.length-1].observe()):t1.splice(t1.indexOf(r),1)}}([null==J?void 0:J.current])},[ei.isOpen,J]),{state:ei,Component:d||"div",children:b,classNames:Y,showArrow:_,triggerRef:ee,placement:er,isNonModal:C,popoverRef:J,portalContainer:R,isOpen:ei.isOpen,onClose:ei.close,disableAnimation:et,shouldBlockScroll:M,backdrop:null!=(n=e.backdrop)?n:"transparent",motionProps:V,getBackdropProps:eh,getPopoverProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:J,...(0,x.v)(en,G,e),style:(0,x.v)(en.style,G.style,e.style)}},getTriggerProps:ev,getDialogProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{"data-slot":"base","data-open":(0,h.sE)(ei.isOpen),"data-focus":(0,h.sE)(ep),"data-arrow":(0,h.sE)(_),"data-focus-visible":(0,h.sE)(ec),"data-placement":el?tq(el,B):void 0,...(0,x.v)(ed,T,e),className:eu.base({class:(0,g.$)(em)}),style:{outline:"none"}}},getContentProps:ef}}({...s,ref:t}),O=u.useRef(null),{dialogProps:M,titleProps:A}=function(e,t){let a,i,{role:n="dialog"}=e,o=(0,ep.X1)();o=e["aria-label"]?void 0:o;let l=(0,u.useRef)(!1);return(0,u.useEffect)(()=>{if(t.current&&!t.current.contains(document.activeElement)){(0,aL.l)(t.current);let e=setTimeout(()=>{document.activeElement===t.current&&(l.current=!0,t.current&&(t.current.blur(),(0,aL.l)(t.current)),l.current=!1)},500);return()=>{clearTimeout(e)}}},[t]),i=null==(a=(0,u.useContext)(t7))?void 0:a.setContain,(0,tB.N)(()=>{null==i||i(!0)},[i]),{dialogProps:{...(0,ej.$)(e,{labelable:!0}),role:n,tabIndex:-1,"aria-labelledby":e["aria-labelledby"]||o,onBlur:e=>{l.current&&e.stopPropagation()}},titleProps:{id:o}}}({},O),F=C({...!r&&{ref:O},...M}),R=u.useMemo(()=>"transparent"===b?null:w?(0,e_.jsx)("div",{...S()}):(0,e_.jsx)(aT.F,{features:aK,children:(0,e_.jsx)(aB.m.div,{animate:"enter",exit:"exit",initial:"exit",variants:a_.fade,...S()})}),[b,w,S]);return(0,e_.jsxs)(t8,{portalContainer:y,children:[!D&&R,(0,e_.jsx)(c,{...j(),children:(0,e_.jsxs)(aW,{disableAnimation:w,motionProps:E,placement:m,tabIndex:-1,transformOrigin:n,...F,children:[!D&&(0,e_.jsx)(aI,{onDismiss:d.close}),(0,e_.jsx)("div",{...P(),children:"function"==typeof a?a(A):a}),(0,e_.jsx)(aI,{onDismiss:d.close})]})})]})});aq.displayName="NextUI.FreeSoloPopover";var aH=({strokeWidth:e=1.5,...t})=>(0,e_.jsx)("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",role:"presentation",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:e,viewBox:"0 0 24 24",width:"1em",...t,children:(0,e_.jsx)("path",{d:"m6 9 6 6 6-6"})}),a$=a(28920),aV=a(41907),aU=a(60760),aY=(0,l.Rf)(function(e,t){let{Component:a,state:i,label:n,hasHelper:r,isLoading:s,triggerRef:w,selectorIcon:z=(0,e_.jsx)(aH,{}),description:D,errorMessage:P,isInvalid:O,startContent:M,endContent:A,placeholder:F,renderValue:R,shouldLabelBeOutside:I,disableAnimation:T,getBaseProps:B,getLabelProps:N,getTriggerProps:_,getValueProps:L,getListboxProps:K,getPopoverProps:W,getSpinnerProps:q,getMainWrapperProps:H,getInnerWrapperProps:$,getHiddenSelectProps:V,getHelperWrapperProps:U,getListboxWrapperProps:Y,getDescriptionProps:X,getErrorMessageProps:G,getSelectorIconProps:J}=function(e){var t,a,i,n,r,s;let w=(0,o.o)(),{validationBehavior:z}=(0,eO.CC)(eM.c)||{},[D,P]=(0,l.rE)(e,c.variantKeys),O=null!=(a=null!=(t=e.disableAnimation)?t:null==w?void 0:w.disableAnimation)&&a,{ref:M,as:A,label:F,name:R,isLoading:I,selectorIcon:T,isOpen:B,defaultOpen:N,onOpenChange:_,startContent:L,endContent:K,description:W,renderValue:q,onSelectionChange:H,placeholder:$,isVirtualized:V,itemHeight:U=36,maxListboxHeight:Y=256,children:X,disallowEmptySelection:G=!1,selectionMode:J="single",spinnerRef:Z,scrollRef:Q,popoverProps:ee={},scrollShadowProps:et={},listboxProps:ea={},spinnerProps:ei={},validationState:en,onChange:eo,onClose:el,className:er,classNames:es,validationBehavior:ed=null!=(i=null!=z?z:null==w?void 0:w.validationBehavior)?i:"native",hideEmptyContent:eu=!1,...em}=D,ef=(0,p.zD)(Q),eg={popoverProps:(0,x.v)({placement:"bottom",triggerScaleOnOpen:!1,offset:5,disableAnimation:O},ee),scrollShadowProps:(0,x.v)({ref:ef,isEnabled:null==(n=e.showScrollIndicators)||n,hideScrollBar:!0,offset:15},et),listboxProps:(0,x.v)({disableAnimation:O},ea)},ev=A||"button",eh="string"==typeof ev,ey=(0,p.zD)(M),ek=(0,u.useRef)(null),eE=(0,u.useRef)(null),eF=(0,u.useRef)(null),eR=function({validate:e,validationBehavior:t,...a}){let[i,n]=(0,u.useState)(!1),[o,l]=(0,u.useState)(null),r=function(e){let t=k(e),[a,i]=(0,u.useState)(null),[n,o]=(0,u.useState)([]),l=()=>{o([]),t.close()};return{focusStrategy:a,...t,open(e=null){i(e),t.open()},toggle(e=null){i(e),t.toggle()},close(){l()},expandedKeysStack:n,openSubmenu:(e,t)=>{o(a=>t>a.length?a:[...a.slice(0,t),e])},closeSubmenu:(e,t)=>{o(a=>a[t]===e?a.slice(0,t):a)}}}(a),s=function(e){let{collection:t,disabledKeys:a,selectionManager:i,selectionManager:{setSelectedKeys:n,selectedKeys:o,selectionMode:l}}=(0,y.p)(e),r=(0,u.useMemo)(()=>e.isLoading||0===o.size?[]:Array.from(o).filter(Boolean).filter(e=>!t.getItem(e)),[o,t]),s=0!==o.size?Array.from(o).map(e=>t.getItem(e)).filter(Boolean):null;return r.length&&console.warn(`Select: Keys "${r.join(", ")}" passed to "selectedKeys" are not present in the collection.`),{collection:t,disabledKeys:a,selectionManager:i,selectionMode:l,selectedKeys:o,setSelectedKeys:n.bind(i),selectedItems:s}}({...a,onSelectionChange:e=>{null!=a.onSelectionChange&&("all"===e?a.onSelectionChange(new Set(s.collection.getKeys())):a.onSelectionChange(e)),"single"===a.selectionMode&&r.close()}}),c=(0,E.KZ)({...a,validationBehavior:t,validate:t=>{if(!e)return;let i=Array.from(t);return e("single"===a.selectionMode?i[0]:i)},value:s.selectedKeys}),p=0===s.collection.size&&a.hideEmptyContent;return{...c,...s,...r,focusStrategy:o,close(){r.close()},open(e=null){p||(l(e),r.open())},toggle(e=null){p||(l(e),r.toggle())},isFocused:i,setFocused:n}}({...D,isOpen:B,selectionMode:J,disallowEmptySelection:G,validationBehavior:ed,children:X,isRequired:e.isRequired,isDisabled:e.isDisabled,isInvalid:e.isInvalid,defaultOpen:N,hideEmptyContent:eu,onOpenChange:e=>{null==_||_(e),e||null==el||el()},onSelectionChange:e=>{null==H||H(e),eo&&"function"==typeof eo&&eo({target:{...ey.current&&{...ey.current,name:ey.current.name},value:Array.from(e).join(",")}}),eR.commitValidation()}});eR={...eR,...e.isDisabled&&{disabledKeys:new Set([...eR.collection.getKeys()])}},(0,eC.U)(()=>{var e;(null==(e=ey.current)?void 0:e.value)&&eR.setSelectedKeys(new Set([...eR.selectedKeys,ey.current.value]))},[ey.current]);let{labelProps:eI,triggerProps:eT,valueProps:eB,menuProps:eN,descriptionProps:e_,errorMessageProps:eL,isInvalid:eK,validationErrors:eW,validationDetails:eq}=function(e,t,a){let{disallowEmptySelection:i,isDisabled:n}=e,o=j({usage:"search",sensitivity:"base"}),l=(0,u.useMemo)(()=>new ez(t.collection,t.disabledKeys,null,o),[t.collection,t.disabledKeys,o]),{menuTriggerProps:r,menuProps:s}=function(e,t,a){var i;let{type:n="menu",isDisabled:o,trigger:l="press"}=e,r=(0,ep.Bi)(),{triggerProps:s,overlayProps:c}=ew({type:n},t,a),p=ex((i=ec)&&i.__esModule?i.default:i,"@react-aria/menu"),{longPressProps:d}=(0,eb.H)({isDisabled:o||"longPress"!==l,accessibilityDescription:p.format("longPressMessage"),onLongPressStart(){t.close()},onLongPress(){t.open("first")}});return delete s.onPress,{menuTriggerProps:{...s,..."press"===l?{onPressStart(e){"touch"===e.pointerType||"keyboard"===e.pointerType||o||t.open("virtual"===e.pointerType?"first":null)},onPress(e){"touch"!==e.pointerType||o||t.toggle()}}:d,id:r,onKeyDown:e=>{if(!o&&("longPress"!==l||e.altKey)&&a&&a.current)switch(e.key){case"Enter":case" ":if("longPress"===l)return;case"ArrowDown":"continuePropagation"in e||e.stopPropagation(),e.preventDefault(),t.toggle("first");break;case"ArrowUp":"continuePropagation"in e||e.stopPropagation(),e.preventDefault(),t.toggle("last");break;default:"continuePropagation"in e&&e.continuePropagation()}}},menuProps:{...c,"aria-labelledby":r,autoFocus:t.focusStrategy||!0,onClose:t.close}}}({isDisabled:n,type:"listbox"},t,a),{typeSelectProps:c}=(0,eD.I)({keyboardDelegate:l,selectionManager:t.selectionManager,onTypeSelect(e){t.setSelectedKeys([e])}}),{isInvalid:p,validationErrors:d,validationDetails:m}=t.displayValidation,{labelProps:f,fieldProps:g,descriptionProps:v,errorMessageProps:h}=(0,C.M)({...e,labelElementType:"span",isInvalid:p,errorMessage:e.errorMessage||d});c.onKeyDown=c.onKeyDownCapture,delete c.onKeyDownCapture;let b=(0,ej.$)(e,{labelable:!0}),y=(0,x.v)(c,r,g),w=(0,ep.Bi)();return{labelProps:{...f,onClick:()=>{var t;e.isDisabled||(null==(t=a.current)||t.focus(),(0,S.Cl)("keyboard"))}},triggerProps:(0,x.v)(b,{...y,onKeyDown:(0,eS.c)(y.onKeyDown,e=>{if("single"===t.selectionMode)switch(e.key){case"ArrowLeft":{e.preventDefault();let a=t.selectedKeys.size>0?l.getKeyAbove(t.selectedKeys.values().next().value):l.getFirstKey();a&&t.setSelectedKeys([a]);break}case"ArrowRight":{e.preventDefault();let a=t.selectedKeys.size>0?l.getKeyBelow(t.selectedKeys.values().next().value):l.getFirstKey();a&&t.setSelectedKeys([a])}}},e.onKeyDown),onKeyUp:e.onKeyUp,"aria-labelledby":[w,void 0!==b["aria-label"]?void 0!==b["aria-labelledby"]?b["aria-labelledby"]:y.id:y["aria-labelledby"]].join(" "),onFocus(a){t.isFocused||(e.onFocus&&e.onFocus(a),t.setFocused(!0))},onBlur(a){t.isOpen||(e.onBlur&&e.onBlur(a),t.setFocused(!1))}}),valueProps:{id:w},menuProps:{...s,disallowEmptySelection:i,autoFocus:t.focusStrategy||!0,shouldSelectOnPressUp:!0,shouldFocusOnHover:!0,onBlur:a=>{a.currentTarget.contains(a.relatedTarget)||(e.onBlur&&e.onBlur(a),t.setFocused(!1))},onFocus:null==s?void 0:s.onFocus,"aria-labelledby":[g["aria-labelledby"],y["aria-label"]&&!g["aria-labelledby"]?y.id:null].filter(Boolean).join(" ")},descriptionProps:v,errorMessageProps:h,isInvalid:p,validationErrors:d,validationDetails:m}}({...D,disallowEmptySelection:G,isDisabled:e.isDisabled},eR,ek),eH=e.isInvalid||"invalid"===en||eK,{isPressed:e$,buttonProps:eV}=(0,m.l)(eT,ek),{focusProps:eU,isFocused:eY,isFocusVisible:eX}=(0,f.o)(),{isHovered:eG,hoverProps:eJ}=(0,b.M)({isDisabled:e.isDisabled}),eZ=(0,u.useMemo)(()=>{var t;return e.labelPlacement&&"inside"!==e.labelPlacement||F?null!=(t=e.labelPlacement)?t:"inside":"outside"},[e.labelPlacement,F]),eQ=!!$,e0="outside-left"===eZ||"outside"===eZ&&(!(eQ||W)||!!e.isMultiline),e1="inside"===eZ,e4="outside-left"===eZ,e3=eR.isOpen||eQ||!!(null==(r=eR.selectedItems)?void 0:r.length)||!!L||!!K||!!e.isMultiline,e2=!!(null==(s=eR.selectedItems)?void 0:s.length),e5=!!F,e6=(0,g.$)(null==es?void 0:es.base,er),e7=(0,u.useMemo)(()=>c({...P,isInvalid:eH,labelPlacement:eZ,disableAnimation:O,className:er}),[(0,v.t6)(P),eH,eZ,O,er]);(0,u.useEffect)(()=>{if(eR.isOpen&&eF.current&&eE.current){let e=eE.current.querySelector("[aria-selected=true] [data-label=true]"),t=ef.current;if(e&&t&&e.parentElement){let a=(null==t?void 0:t.getBoundingClientRect()).height;t.scrollTop=e.parentElement.offsetTop-a/2+e.parentElement.clientHeight/2}}},[eR.isOpen,O]);let e8="function"==typeof D.errorMessage?D.errorMessage({isInvalid:eH,validationErrors:eW,validationDetails:eq}):D.errorMessage||(null==eW?void 0:eW.join(" ")),e9=!!W||!!e8;(0,u.useEffect)(()=>{if(eR.isOpen&&eF.current&&ek.current){let e=ek.current.getBoundingClientRect();eF.current.style.width=e.width+"px"}},[eR.isOpen]);let te=(0,u.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{"data-slot":"base","data-filled":(0,h.sE)(e3),"data-has-value":(0,h.sE)(e2),"data-has-label":(0,h.sE)(e5),"data-has-helper":(0,h.sE)(e9),"data-invalid":(0,h.sE)(eH),className:e7.base({class:(0,g.$)(e6,e.className)}),...e}},[e7,e9,e2,e5,e3,e6]),tt=(0,u.useCallback)(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:ek,"data-slot":"trigger","data-open":(0,h.sE)(eR.isOpen),"data-disabled":(0,h.sE)(null==e?void 0:e.isDisabled),"data-focus":(0,h.sE)(eY),"data-pressed":(0,h.sE)(e$),"data-focus-visible":(0,h.sE)(eX),"data-hover":(0,h.sE)(eG),className:e7.trigger({class:null==es?void 0:es.trigger}),...(0,x.v)(eV,eU,eJ,(0,d.$)(em,{enabled:eh}),(0,d.$)(t))}},[e7,ek,eR.isOpen,null==es?void 0:es.trigger,null==e?void 0:e.isDisabled,eY,e$,eX,eG,eV,eU,eJ,em,eh]),ta=(0,u.useCallback)(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{state:eR,triggerRef:ek,selectRef:ey,selectionMode:J,label:null==e?void 0:e.label,name:null==e?void 0:e.name,isRequired:null==e?void 0:e.isRequired,autoComplete:null==e?void 0:e.autoComplete,isDisabled:null==e?void 0:e.isDisabled,onChange:eo,...t}},[eR,J,null==e?void 0:e.label,null==e?void 0:e.autoComplete,null==e?void 0:e.name,null==e?void 0:e.isDisabled,ek]),ti=(0,u.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{"data-slot":"label",className:e7.label({class:(0,g.$)(null==es?void 0:es.label,e.className)}),...eI,...e}},[e7,null==es?void 0:es.label,eI]),tn=(0,u.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{"data-slot":"value",className:e7.value({class:(0,g.$)(null==es?void 0:es.value,e.className)}),...eB,...e}},[e7,null==es?void 0:es.value,eB]),to=(0,u.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{"data-slot":"listboxWrapper",className:e7.listboxWrapper({class:(0,g.$)(null==es?void 0:es.listboxWrapper,null==e?void 0:e.className)}),style:{maxHeight:null!=Y?Y:256,...e.style},...(0,x.v)(eg.scrollShadowProps,e)}},[e7.listboxWrapper,null==es?void 0:es.listboxWrapper,eg.scrollShadowProps,Y]),tl=(0,u.useCallback)(function(){var e,t;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=(0,x.v)(eg.popoverProps,a);return{state:eR,triggerRef:ek,ref:eF,"data-slot":"popover",scrollRef:eE,triggerType:"listbox",classNames:{content:e7.popoverContent({class:(0,g.$)(null==es?void 0:es.popoverContent,a.className)})},...i,offset:eR.selectedItems&&eR.selectedItems.length>0?1e-8*eR.selectedItems.length+((null==(e=eg.popoverProps)?void 0:e.offset)||0):null==(t=eg.popoverProps)?void 0:t.offset,shouldCloseOnInteractOutside:(null==i?void 0:i.shouldCloseOnInteractOutside)?i.shouldCloseOnInteractOutside:e=>eP(e,ey,eR)}},[e7,null==es?void 0:es.popoverContent,eg.popoverProps,ek,eR,eR.selectedItems]),tr=(0,u.useCallback)(()=>({"data-slot":"selectorIcon","aria-hidden":(0,h.sE)(!0),"data-open":(0,h.sE)(eR.isOpen),className:e7.selectorIcon({class:null==es?void 0:es.selectorIcon})}),[e7,null==es?void 0:es.selectorIcon,eR.isOpen]),ts=(0,u.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{...e,"data-slot":"innerWrapper",className:e7.innerWrapper({class:(0,g.$)(null==es?void 0:es.innerWrapper,null==e?void 0:e.className)})}},[e7,null==es?void 0:es.innerWrapper]),tc=(0,u.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{...e,"data-slot":"helperWrapper",className:e7.helperWrapper({class:(0,g.$)(null==es?void 0:es.helperWrapper,null==e?void 0:e.className)})}},[e7,null==es?void 0:es.helperWrapper]),tp=(0,u.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{...e,...e_,"data-slot":"description",className:e7.description({class:(0,g.$)(null==es?void 0:es.description,null==e?void 0:e.className)})}},[e7,null==es?void 0:es.description]),td=(0,u.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{...e,"data-slot":"mainWrapper",className:e7.mainWrapper({class:(0,g.$)(null==es?void 0:es.mainWrapper,null==e?void 0:e.className)})}},[e7,null==es?void 0:es.mainWrapper]),tu=(0,u.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{...e,...eL,"data-slot":"error-message",className:e7.errorMessage({class:(0,g.$)(null==es?void 0:es.errorMessage,null==e?void 0:e.className)})}},[e7,eL,null==es?void 0:es.errorMessage]),tm=(0,u.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{"aria-hidden":(0,h.sE)(!0),"data-slot":"spinner",color:"current",size:"sm",...ei,...e,ref:Z,className:e7.spinner({class:(0,g.$)(null==es?void 0:es.spinner,null==e?void 0:e.className)})}},[e7,Z,ei,null==es?void 0:es.spinner]);return eA.set(eR,{isDisabled:null==e?void 0:e.isDisabled,isRequired:null==e?void 0:e.isRequired,name:null==e?void 0:e.name,isInvalid:eH,validationBehavior:ed}),{Component:ev,domRef:ey,state:eR,label:F,name:R,triggerRef:ek,isLoading:I,placeholder:$,startContent:L,endContent:K,description:W,selectorIcon:T,hasHelper:e9,labelPlacement:eZ,hasPlaceholder:eQ,renderValue:q,selectionMode:J,disableAnimation:O,isOutsideLeft:e4,shouldLabelBeOutside:e0,shouldLabelBeInside:e1,isInvalid:eH,errorMessage:e8,getBaseProps:te,getTriggerProps:tt,getLabelProps:ti,getValueProps:tn,getListboxProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=null!=V?V:eR.collection.size>50;return{state:eR,ref:eE,isVirtualized:t,virtualization:t?{maxListboxHeight:Y,itemHeight:U}:void 0,"data-slot":"listbox",className:e7.listbox({class:(0,g.$)(null==es?void 0:es.listbox,null==e?void 0:e.className)}),scrollShadowProps:eg.scrollShadowProps,...(0,x.v)(eg.listboxProps,e,eN)}},getPopoverProps:tl,getSpinnerProps:tm,getMainWrapperProps:td,getListboxWrapperProps:to,getHiddenSelectProps:ta,getInnerWrapperProps:ts,getHelperWrapperProps:tc,getDescriptionProps:tp,getErrorMessageProps:tu,getSelectorIconProps:tr}}({...e,ref:t}),Z=n?(0,e_.jsx)("label",{...N(),children:n}):null,Q=(0,u.cloneElement)(z,J()),ee=(0,u.useMemo)(()=>{let e=O&&P,t=e||D;return r&&t?(0,e_.jsx)("div",{...U(),children:e?(0,e_.jsx)("div",{...G(),children:P}):(0,e_.jsx)("div",{...X(),children:D})}):null},[r,O,P,D,U,G,X]),et=(0,u.useMemo)(()=>{var e;return(null==(e=i.selectedItems)?void 0:e.length)?R&&"function"==typeof R?R([...i.selectedItems].map(e=>({key:e.key,data:e.value,type:e.type,props:e.props,textValue:e.textValue,rendered:e.rendered,"aria-label":e["aria-label"]}))):i.selectedItems.map(e=>e.textValue).join(", "):F},[i.selectedItems,R,F]),ea=(0,u.useMemo)(()=>s?(0,e_.jsx)(a$.o,{...q()}):Q,[s,Q,q]),ei=(0,u.useMemo)(()=>i.isOpen?(0,e_.jsx)(aq,{...W(),children:(0,e_.jsx)(aV.H,{...Y(),children:(0,e_.jsx)(tg,{...K()})})}):null,[i.isOpen,W,i,w,Y,K]);return(0,e_.jsxs)("div",{...B(),children:[(0,e_.jsx)(eL,{...V()}),I?Z:null,(0,e_.jsxs)("div",{...H(),children:[(0,e_.jsxs)(a,{..._(),children:[I?null:Z,(0,e_.jsxs)("div",{...$(),children:[M,(0,e_.jsx)("span",{...L(),children:et}),A&&i.selectedItems&&(0,e_.jsx)(eB,{elementType:"span",children:","}),A]}),ea]}),ee]}),T?ei:(0,e_.jsx)(aU.N,{children:ei})]})})},12462:(e,t)=>{"use strict";t.__esModule=!0,t.default=function(e,t){if(e&&t){var a=Array.isArray(t)?t:t.split(",");if(0===a.length)return!0;var i=e.name||"",n=(e.type||"").toLowerCase(),o=n.replace(/\/.*$/,"");return a.some(function(e){var t=e.trim().toLowerCase();return"."===t.charAt(0)?i.toLowerCase().endsWith(t):t.endsWith("/*")?o===t.replace(/\/.*$/,""):n===t})}return!0}},16923:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(40157).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},29270:(e,t,a)=>{"use strict";a.d(t,{P:()=>O});var i=a(76917),n=a(672),o=a(56973),l=a(81627),r=a(12115);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var i in a)({}).hasOwnProperty.call(a,i)&&(e[i]=a[i])}return e}).apply(null,arguments)}var c=r.useLayoutEffect,p=function(e){var t=r.useRef(e);return c(function(){t.current=e}),t},d=function(e,t){if("function"==typeof e)return void e(t);e.current=t},u=function(e,t){var a=r.useRef();return r.useCallback(function(i){e.current=i,a.current&&d(a.current,null),a.current=t,t&&d(t,i)},[t])},m={"min-height":"0","max-height":"none",height:"0",visibility:"hidden",overflow:"hidden",position:"absolute","z-index":"-1000",top:"0",right:"0",display:"block"},f=function(e){Object.keys(m).forEach(function(t){e.style.setProperty(t,m[t],"important")})},g=null,v=function(e,t){var a=e.scrollHeight;return"border-box"===t.sizingStyle.boxSizing?a+t.borderSize:a-t.paddingSize},h=function(){},x=["borderBottomWidth","borderLeftWidth","borderRightWidth","borderTopWidth","boxSizing","fontFamily","fontSize","fontStyle","fontWeight","letterSpacing","lineHeight","paddingBottom","paddingLeft","paddingRight","paddingTop","tabSize","textIndent","textRendering","textTransform","width","wordBreak","wordSpacing","scrollbarGutter"],b=!!document.documentElement.currentStyle,y=function(e){var t=window.getComputedStyle(e);if(null===t)return null;var a=x.reduce(function(e,a){return e[a]=t[a],e},{}),i=a.boxSizing;if(""===i)return null;b&&"border-box"===i&&(a.width=parseFloat(a.width)+parseFloat(a.borderRightWidth)+parseFloat(a.borderLeftWidth)+parseFloat(a.paddingRight)+parseFloat(a.paddingLeft)+"px");var n=parseFloat(a.paddingBottom)+parseFloat(a.paddingTop),o=parseFloat(a.borderBottomWidth)+parseFloat(a.borderTopWidth);return{sizingStyle:a,paddingSize:n,borderSize:o}};function w(e,t,a){var i=p(a);r.useLayoutEffect(function(){var a=function(e){return i.current(e)};if(e)return e.addEventListener(t,a),function(){return e.removeEventListener(t,a)}},[])}var k=function(e,t){w(document.body,"reset",function(a){e.current.form===a.target&&t(a)})},E=function(e){w(window,"resize",e)},z=function(e){w(document.fonts,"loadingdone",e)},D=["cacheMeasurements","maxRows","minRows","onChange","onHeightChange"],j=r.forwardRef(function(e,t){var a=e.cacheMeasurements,i=e.maxRows,n=e.minRows,o=e.onChange,l=void 0===o?h:o,c=e.onHeightChange,p=void 0===c?h:c,d=function(e,t){if(null==e)return{};var a={};for(var i in e)if(({}).hasOwnProperty.call(e,i)){if(-1!==t.indexOf(i))continue;a[i]=e[i]}return a}(e,D),m=void 0!==d.value,x=r.useRef(null),b=u(x,t),w=r.useRef(0),j=r.useRef(),S=function(){var e=x.current,t=a&&j.current?j.current:y(e);if(t){j.current=t;var o,l,r,s,c,d,u,m,h,b,k,E=(o=e.value||e.placeholder||"x",void 0===(l=n)&&(l=1),void 0===(r=i)&&(r=1/0),g||((g=document.createElement("textarea")).setAttribute("tabindex","-1"),g.setAttribute("aria-hidden","true"),f(g)),null===g.parentNode&&document.body.appendChild(g),s=t.paddingSize,c=t.borderSize,u=(d=t.sizingStyle).boxSizing,Object.keys(d).forEach(function(e){g.style[e]=d[e]}),f(g),g.value=o,m=v(g,t),g.value=o,m=v(g,t),g.value="x",b=(h=g.scrollHeight-s)*l,"border-box"===u&&(b=b+s+c),m=Math.max(b,m),k=h*r,"border-box"===u&&(k=k+s+c),[m=Math.min(k,m),h]),z=E[0],D=E[1];w.current!==z&&(w.current=z,e.style.setProperty("height",z+"px","important"),p(z,{rowHeight:D}))}};return r.useLayoutEffect(S),k(x,function(){if(!m){var e=x.current.value;requestAnimationFrame(function(){var t=x.current;t&&e!==t.value&&S()})}}),E(S),z(S),r.createElement("textarea",s({},d,{onChange:function(e){m||S(),l(e)},ref:b}))}),S=a(1529),C=a(95155),P=(0,o.Rf)((e,t)=>{let{style:a,minRows:o=3,maxRows:s=8,cacheMeasurements:c=!1,disableAutosize:p=!1,onHeightChange:d,...u}=e,{Component:m,label:f,description:g,startContent:v,endContent:h,hasHelper:x,shouldLabelBeOutside:b,shouldLabelBeInside:y,isInvalid:w,errorMessage:k,getBaseProps:E,getLabelProps:z,getInputProps:D,getInnerWrapperProps:P,getInputWrapperProps:O,getHelperWrapperProps:M,getDescriptionProps:A,getErrorMessageProps:F,isClearable:R,getClearButtonProps:I}=(0,i.G)({...u,ref:t,isMultiline:!0}),[T,B]=(0,r.useState)(o>1),[N,_]=(0,r.useState)(!1),L=f?(0,C.jsx)("label",{...z(),children:f}):null,K=D(),W=p?(0,C.jsx)("textarea",{...K,style:(0,l.v)(K.style,null!=a?a:{})}):(0,C.jsx)(j,{...K,cacheMeasurements:c,"data-hide-scroll":(0,n.sE)(!N),maxRows:s,minRows:o,style:(0,l.v)(K.style,null!=a?a:{}),onHeightChange:(e,t)=>{1===o&&B(e>=2*t.rowHeight),s>o&&_(e>=s*t.rowHeight),null==d||d(e,t)}}),q=(0,r.useMemo)(()=>R?(0,C.jsx)("button",{...I(),children:(0,C.jsx)(S.o,{})}):null,[R,I]),H=(0,r.useMemo)(()=>v||h?(0,C.jsxs)("div",{...P(),children:[v,W,h]}):(0,C.jsx)("div",{...P(),children:W}),[v,K,h,P]),$=w&&k,V=$||g;return(0,C.jsxs)(m,{...E(),children:[b?L:null,(0,C.jsxs)("div",{...O(),"data-has-multiple-rows":(0,n.sE)(T),children:[y?L:null,H,q]}),x&&V?(0,C.jsx)("div",{...M(),children:$?(0,C.jsx)("div",{...F(),children:k}):(0,C.jsx)("div",{...A(),children:g})}):null]})});P.displayName="NextUI.Textarea";var O=P},36545:(e,t,a)=>{"use strict";a.d(t,{P:()=>d});var i=a(14060),n=a(55457),o=a(23905),l=a(25646),r=a(53292),s=a(53880),c=a(61710);let p=(0,s.C)({...n.W,...l.n,...o.$,...r.Z},c.J),d=(0,i.I)(p)},38637:(e,t,a)=>{e.exports=a(79399)()},41907:(e,t,a)=>{"use strict";a.d(t,{H:()=>p});var i=a(56973),n=a(47956),o=a(6548),l=a(81467),r=a(12115),s=a(95155),c=(0,i.Rf)((e,t)=>{let{Component:a,children:c,getBaseProps:p}=function(e){var t;let[a,s]=(0,i.rE)(e,n.Q.variantKeys),{ref:c,as:p,children:d,className:u,style:m,size:f=40,offset:g=0,visibility:v="auto",isEnabled:h=!0,onVisibilityChange:x,...b}=a,y=(0,o.zD)(c);!function(e={}){let{domRef:t,isEnabled:a=!0,overflowCheck:i="vertical",visibility:n="auto",offset:o=0,onVisibilityChange:s,updateDeps:c=[]}=e,p=(0,r.useRef)(n);(0,r.useEffect)(()=>{let e=null==t?void 0:t.current;if(!e||!a)return;let r=(t,a,i,o,r)=>{if("auto"===n){let t=`${o}${(0,l.ZH)(r)}Scroll`;a&&i?(e.dataset[t]="true",e.removeAttribute(`data-${o}-scroll`),e.removeAttribute(`data-${r}-scroll`)):(e.dataset[`${o}Scroll`]=a.toString(),e.dataset[`${r}Scroll`]=i.toString(),e.removeAttribute(`data-${o}-${r}-scroll`))}else{let e=a&&i?"both":a?o:i?r:"none";e!==p.current&&(null==s||s(e),p.current=e)}},c=()=>{for(let{type:t,prefix:a,suffix:n}of[{type:"vertical",prefix:"top",suffix:"bottom"},{type:"horizontal",prefix:"left",suffix:"right"}])if(i===t||"both"===i){let i="vertical"===t?e.scrollTop>o:e.scrollLeft>o,l="vertical"===t?e.scrollTop+e.clientHeight+o<e.scrollHeight:e.scrollLeft+e.clientWidth+o<e.scrollWidth;r(t,i,l,a,n)}},d=()=>{["top","bottom","top-bottom","left","right","left-right"].forEach(t=>{e.removeAttribute(`data-${t}-scroll`)})};return c(),e.addEventListener("scroll",c),"auto"!==n&&(d(),"both"===n?(e.dataset.topBottomScroll=String("vertical"===i),e.dataset.leftRightScroll=String("horizontal"===i)):(e.dataset.topBottomScroll="false",e.dataset.leftRightScroll="false",["top","bottom","left","right"].forEach(t=>{e.dataset[`${t}Scroll`]=String(n===t)}))),()=>{e.removeEventListener("scroll",c),d()}},[...c,a,n,i,s,t])}({domRef:y,offset:g,visibility:v,isEnabled:h,onVisibilityChange:x,updateDeps:[d],overflowCheck:null!=(t=e.orientation)?t:"vertical"});let w=(0,r.useMemo)(()=>(0,n.Q)({...s,className:u}),[(0,l.t6)(s),u]);return{Component:p||"div",styles:w,domRef:y,children:d,getBaseProps:function(){var t;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:y,className:w,"data-orientation":null!=(t=e.orientation)?t:"vertical",style:{"--scroll-shadow-size":"".concat(f,"px"),...m,...a.style},...b,...a}}}}({...e,ref:t});return(0,s.jsx)(a,{...p(),children:c})});c.displayName="NextUI.ScrollShadow";var p=c},46710:(e,t,a)=>{"use strict";a.d(t,{VB:()=>Q});var i=a(12115),n=a(38637);function o(e,t,a,i){return new(a||(a=Promise))(function(n,o){function l(e){try{s(i.next(e))}catch(e){o(e)}}function r(e){try{s(i.throw(e))}catch(e){o(e)}}function s(e){var t;e.done?n(e.value):((t=e.value)instanceof a?t:new a(function(e){e(t)})).then(l,r)}s((i=i.apply(e,t||[])).next())})}Object.create;Object.create,"function"==typeof SuppressedError&&SuppressedError;let l=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function r(e,t,a){let i=function(e){let{name:t}=e;if(t&&-1!==t.lastIndexOf(".")&&!e.type){let a=t.split(".").pop().toLowerCase(),i=l.get(a);i&&Object.defineProperty(e,"type",{value:i,writable:!1,configurable:!1,enumerable:!0})}return e}(e),{webkitRelativePath:n}=e,o="string"==typeof t?t:"string"==typeof n&&n.length>0?n:`./${e.name}`;return"string"!=typeof i.path&&s(i,"path",o),void 0!==a&&Object.defineProperty(i,"handle",{value:a,writable:!1,configurable:!1,enumerable:!0}),s(i,"relativePath",o),i}function s(e,t,a){Object.defineProperty(e,t,{value:a,writable:!1,configurable:!1,enumerable:!0})}let c=[".DS_Store","Thumbs.db"];function p(e){return"object"==typeof e&&null!==e}function d(e){return e.filter(e=>-1===c.indexOf(e.name))}function u(e){if(null===e)return[];let t=[];for(let a=0;a<e.length;a++){let i=e[a];t.push(i)}return t}function m(e){if("function"!=typeof e.webkitGetAsEntry)return f(e);let t=e.webkitGetAsEntry();return t&&t.isDirectory?v(t):f(e,t)}function f(e,t){return o(this,void 0,void 0,function*(){var a;if(globalThis.isSecureContext&&"function"==typeof e.getAsFileSystemHandle){let t=yield e.getAsFileSystemHandle();if(null===t)throw Error(`${e} is not a File`);if(void 0!==t){let e=yield t.getFile();return e.handle=t,r(e)}}let i=e.getAsFile();if(!i)throw Error(`${e} is not a File`);return r(i,null!=(a=null==t?void 0:t.fullPath)?a:void 0)})}function g(e){return o(this,void 0,void 0,function*(){return e.isDirectory?v(e):function(e){return o(this,void 0,void 0,function*(){return new Promise((t,a)=>{e.file(a=>{t(r(a,e.fullPath))},e=>{a(e)})})})}(e)})}function v(e){let t=e.createReader();return new Promise((e,a)=>{let i=[];!function n(){t.readEntries(t=>o(this,void 0,void 0,function*(){if(t.length){let e=Promise.all(t.map(g));i.push(e),n()}else try{let t=yield Promise.all(i);e(t)}catch(e){a(e)}}),e=>{a(e)})}()})}var h=a(12462);function x(e){return function(e){if(Array.isArray(e))return z(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||E(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,i)}return a}function y(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?b(Object(a),!0).forEach(function(t){w(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):b(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function w(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function k(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var a,i,n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o=[],l=!0,r=!1;try{for(n=n.call(e);!(l=(a=n.next()).done)&&(o.push(a.value),!t||o.length!==t);l=!0);}catch(e){r=!0,i=e}finally{try{l||null==n.return||n.return()}finally{if(r)throw i}}return o}}(e,t)||E(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function E(e,t){if(e){if("string"==typeof e)return z(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);if("Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return z(e,t)}}function z(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}var D="function"==typeof h?h:h.default,j=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=e.split(","),a=t.length>1?"one of ".concat(t.join(", ")):t[0];return{code:"file-invalid-type",message:"File type must be ".concat(a)}},S=function(e){return{code:"file-too-large",message:"File is larger than ".concat(e," ").concat(1===e?"byte":"bytes")}},C=function(e){return{code:"file-too-small",message:"File is smaller than ".concat(e," ").concat(1===e?"byte":"bytes")}},P={code:"too-many-files",message:"Too many files"};function O(e,t){var a="application/x-moz-file"===e.type||D(e,t);return[a,a?null:j(t)]}function M(e,t,a){if(A(e.size)){if(A(t)&&A(a)){if(e.size>a)return[!1,S(a)];if(e.size<t)return[!1,C(t)]}else if(A(t)&&e.size<t)return[!1,C(t)];else if(A(a)&&e.size>a)return[!1,S(a)]}return[!0,null]}function A(e){return null!=e}function F(e){return"function"==typeof e.isPropagationStopped?e.isPropagationStopped():void 0!==e.cancelBubble&&e.cancelBubble}function R(e){return e.dataTransfer?Array.prototype.some.call(e.dataTransfer.types,function(e){return"Files"===e||"application/x-moz-file"===e}):!!e.target&&!!e.target.files}function I(e){e.preventDefault()}function T(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return function(e){for(var a=arguments.length,i=Array(a>1?a-1:0),n=1;n<a;n++)i[n-1]=arguments[n];return t.some(function(t){return!F(e)&&t&&t.apply(void 0,[e].concat(i)),F(e)})}}function B(e){return"audio/*"===e||"video/*"===e||"image/*"===e||"text/*"===e||"application/*"===e||/\w+\/[-+.\w]+/g.test(e)}function N(e){return/^.*\.[\w]+$/.test(e)}var _=["children"],L=["open"],K=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],W=["refKey","onChange","onClick"];function q(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var a,i,n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o=[],l=!0,r=!1;try{for(n=n.call(e);!(l=(a=n.next()).done)&&(o.push(a.value),!t||o.length!==t);l=!0);}catch(e){r=!0,i=e}finally{try{l||null==n.return||n.return()}finally{if(r)throw i}}return o}}(e,t)||H(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function H(e,t){if(e){if("string"==typeof e)return $(e,t);var a=Object.prototype.toString.call(e).slice(8,-1);if("Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return $(e,t)}}function $(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}function V(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,i)}return a}function U(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?V(Object(a),!0).forEach(function(t){Y(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):V(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function Y(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function X(e,t){if(null==e)return{};var a,i,n=function(e,t){if(null==e)return{};var a,i,n={},o=Object.keys(e);for(i=0;i<o.length;i++)a=o[i],t.indexOf(a)>=0||(n[a]=e[a]);return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)a=o[i],!(t.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(n[a]=e[a])}return n}var G=(0,i.forwardRef)(function(e,t){var a=e.children,n=Q(X(e,_)),o=n.open,l=X(n,L);return(0,i.useImperativeHandle)(t,function(){return{open:o}},[o]),i.createElement(i.Fragment,null,a(U(U({},l),{},{open:o})))});G.displayName="Dropzone";var J={disabled:!1,getFilesFromEvent:function(e){return o(this,void 0,void 0,function*(){var t;if(p(e)&&p(e.dataTransfer))return function(e,t){return o(this,void 0,void 0,function*(){if(e.items){let a=u(e.items).filter(e=>"file"===e.kind);return"drop"!==t?a:d(function e(t){return t.reduce((t,a)=>[...t,...Array.isArray(a)?e(a):[a]],[])}((yield Promise.all(a.map(m)))))}return d(u(e.files).map(e=>r(e)))})}(e.dataTransfer,e.type);if(p(t=e)&&p(t.target))return u(e.target.files).map(e=>r(e));return Array.isArray(e)&&e.every(e=>"getFile"in e&&"function"==typeof e.getFile)?function(e){return o(this,void 0,void 0,function*(){return(yield Promise.all(e.map(e=>e.getFile()))).map(e=>r(e))})}(e):[]})},maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};G.defaultProps=J,G.propTypes={children:n.func,accept:n.objectOf(n.arrayOf(n.string)),multiple:n.bool,preventDropOnDocument:n.bool,noClick:n.bool,noKeyboard:n.bool,noDrag:n.bool,noDragEventsBubbling:n.bool,minSize:n.number,maxSize:n.number,maxFiles:n.number,disabled:n.bool,getFilesFromEvent:n.func,onFileDialogCancel:n.func,onFileDialogOpen:n.func,useFsAccessApi:n.bool,autoFocus:n.bool,onDragEnter:n.func,onDragLeave:n.func,onDragOver:n.func,onDrop:n.func,onDropAccepted:n.func,onDropRejected:n.func,onError:n.func,validator:n.func};var Z={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function Q(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=U(U({},J),e),a=t.accept,n=t.disabled,o=t.getFilesFromEvent,l=t.maxSize,r=t.minSize,s=t.multiple,c=t.maxFiles,p=t.onDragEnter,d=t.onDragLeave,u=t.onDragOver,m=t.onDrop,f=t.onDropAccepted,g=t.onDropRejected,v=t.onFileDialogCancel,h=t.onFileDialogOpen,b=t.useFsAccessApi,E=t.autoFocus,z=t.preventDropOnDocument,D=t.noClick,j=t.noKeyboard,S=t.noDrag,C=t.noDragEventsBubbling,_=t.onError,L=t.validator,V=(0,i.useMemo)(function(){return A(a)?Object.entries(a).reduce(function(e,t){var a=k(t,2),i=a[0],n=a[1];return[].concat(x(e),[i],x(n))},[]).filter(function(e){return B(e)||N(e)}).join(","):void 0},[a]),G=(0,i.useMemo)(function(){return A(a)?[{description:"Files",accept:Object.entries(a).filter(function(e){var t=k(e,2),a=t[0],i=t[1],n=!0;return B(a)||(console.warn('Skipped "'.concat(a,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),n=!1),Array.isArray(i)&&i.every(N)||(console.warn('Skipped "'.concat(a,'" because an invalid file extension was provided.')),n=!1),n}).reduce(function(e,t){var a=k(t,2),i=a[0],n=a[1];return y(y({},e),{},w({},i,n))},{})}]:a},[a]),Q=(0,i.useMemo)(function(){return"function"==typeof h?h:et},[h]),ea=(0,i.useMemo)(function(){return"function"==typeof v?v:et},[v]),ei=(0,i.useRef)(null),en=(0,i.useRef)(null),eo=q((0,i.useReducer)(ee,Z),2),el=eo[0],er=eo[1],es=el.isFocused,ec=el.isFileDialogActive,ep=(0,i.useRef)("undefined"!=typeof window&&window.isSecureContext&&b&&"showOpenFilePicker"in window),ed=function(){!ep.current&&ec&&setTimeout(function(){en.current&&(en.current.files.length||(er({type:"closeDialog"}),ea()))},300)};(0,i.useEffect)(function(){return window.addEventListener("focus",ed,!1),function(){window.removeEventListener("focus",ed,!1)}},[en,ec,ea,ep]);var eu=(0,i.useRef)([]),em=function(e){ei.current&&ei.current.contains(e.target)||(e.preventDefault(),eu.current=[])};(0,i.useEffect)(function(){return z&&(document.addEventListener("dragover",I,!1),document.addEventListener("drop",em,!1)),function(){z&&(document.removeEventListener("dragover",I),document.removeEventListener("drop",em))}},[ei,z]),(0,i.useEffect)(function(){return!n&&E&&ei.current&&ei.current.focus(),function(){}},[ei,E,n]);var ef=(0,i.useCallback)(function(e){_?_(e):console.error(e)},[_]),eg=(0,i.useCallback)(function(e){var t;e.preventDefault(),e.persist(),eC(e),eu.current=[].concat(function(e){if(Array.isArray(e))return $(e)}(t=eu.current)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||H(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[e.target]),R(e)&&Promise.resolve(o(e)).then(function(t){if(!F(e)||C){var a,i,n,o,d,u,m,f,g=t.length,v=g>0&&(i=(a={files:t,accept:V,minSize:r,maxSize:l,multiple:s,maxFiles:c,validator:L}).files,n=a.accept,o=a.minSize,d=a.maxSize,u=a.multiple,m=a.maxFiles,f=a.validator,(!!u||!(i.length>1))&&(!u||!(m>=1)||!(i.length>m))&&i.every(function(e){var t=k(O(e,n),1)[0],a=k(M(e,o,d),1)[0],i=f?f(e):null;return t&&a&&!i}));er({isDragAccept:v,isDragReject:g>0&&!v,isDragActive:!0,type:"setDraggedFiles"}),p&&p(e)}}).catch(function(e){return ef(e)})},[o,p,ef,C,V,r,l,s,c,L]),ev=(0,i.useCallback)(function(e){e.preventDefault(),e.persist(),eC(e);var t=R(e);if(t&&e.dataTransfer)try{e.dataTransfer.dropEffect="copy"}catch(e){}return t&&u&&u(e),!1},[u,C]),eh=(0,i.useCallback)(function(e){e.preventDefault(),e.persist(),eC(e);var t=eu.current.filter(function(e){return ei.current&&ei.current.contains(e)}),a=t.indexOf(e.target);-1!==a&&t.splice(a,1),eu.current=t,!(t.length>0)&&(er({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),R(e)&&d&&d(e))},[ei,d,C]),ex=(0,i.useCallback)(function(e,t){var a=[],i=[];e.forEach(function(e){var t=q(O(e,V),2),n=t[0],o=t[1],s=q(M(e,r,l),2),c=s[0],p=s[1],d=L?L(e):null;if(n&&c&&!d)a.push(e);else{var u=[o,p];d&&(u=u.concat(d)),i.push({file:e,errors:u.filter(function(e){return e})})}}),(!s&&a.length>1||s&&c>=1&&a.length>c)&&(a.forEach(function(e){i.push({file:e,errors:[P]})}),a.splice(0)),er({acceptedFiles:a,fileRejections:i,isDragReject:i.length>0,type:"setFiles"}),m&&m(a,i,t),i.length>0&&g&&g(i,t),a.length>0&&f&&f(a,t)},[er,s,V,r,l,c,m,f,g,L]),eb=(0,i.useCallback)(function(e){e.preventDefault(),e.persist(),eC(e),eu.current=[],R(e)&&Promise.resolve(o(e)).then(function(t){(!F(e)||C)&&ex(t,e)}).catch(function(e){return ef(e)}),er({type:"reset"})},[o,ex,ef,C]),ey=(0,i.useCallback)(function(){if(ep.current){er({type:"openDialog"}),Q(),window.showOpenFilePicker({multiple:s,types:G}).then(function(e){return o(e)}).then(function(e){ex(e,null),er({type:"closeDialog"})}).catch(function(e){e instanceof DOMException&&("AbortError"===e.name||e.code===e.ABORT_ERR)?(ea(e),er({type:"closeDialog"})):e instanceof DOMException&&("SecurityError"===e.name||e.code===e.SECURITY_ERR)?(ep.current=!1,en.current?(en.current.value=null,en.current.click()):ef(Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):ef(e)});return}en.current&&(er({type:"openDialog"}),Q(),en.current.value=null,en.current.click())},[er,Q,ea,b,ex,ef,G,s]),ew=(0,i.useCallback)(function(e){ei.current&&ei.current.isEqualNode(e.target)&&(" "===e.key||"Enter"===e.key||32===e.keyCode||13===e.keyCode)&&(e.preventDefault(),ey())},[ei,ey]),ek=(0,i.useCallback)(function(){er({type:"focus"})},[]),eE=(0,i.useCallback)(function(){er({type:"blur"})},[]),ez=(0,i.useCallback)(function(){D||(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.navigator.userAgent;return -1!==e.indexOf("MSIE")||-1!==e.indexOf("Trident/")||-1!==e.indexOf("Edge/")}()?setTimeout(ey,0):ey())},[D,ey]),eD=function(e){return n?null:e},ej=function(e){return j?null:eD(e)},eS=function(e){return S?null:eD(e)},eC=function(e){C&&e.stopPropagation()},eP=(0,i.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,a=e.role,i=e.onKeyDown,o=e.onFocus,l=e.onBlur,r=e.onClick,s=e.onDragEnter,c=e.onDragOver,p=e.onDragLeave,d=e.onDrop,u=X(e,K);return U(U(Y({onKeyDown:ej(T(i,ew)),onFocus:ej(T(o,ek)),onBlur:ej(T(l,eE)),onClick:eD(T(r,ez)),onDragEnter:eS(T(s,eg)),onDragOver:eS(T(c,ev)),onDragLeave:eS(T(p,eh)),onDrop:eS(T(d,eb)),role:"string"==typeof a&&""!==a?a:"presentation"},void 0===t?"ref":t,ei),n||j?{}:{tabIndex:0}),u)}},[ei,ew,ek,eE,ez,eg,ev,eh,eb,j,S,n]),eO=(0,i.useCallback)(function(e){e.stopPropagation()},[]),eM=(0,i.useMemo)(function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.refKey,a=e.onChange,i=e.onClick,n=X(e,W);return U(U({},Y({accept:V,multiple:s,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:eD(T(a,eb)),onClick:eD(T(i,eO)),tabIndex:-1},void 0===t?"ref":t,en)),n)}},[en,a,s,eb,n]);return U(U({},el),{},{isFocused:es&&!n,getRootProps:eP,getInputProps:eM,rootRef:ei,inputRef:en,open:eD(ey)})}function ee(e,t){switch(t.type){case"focus":return U(U({},e),{},{isFocused:!0});case"blur":return U(U({},e),{},{isFocused:!1});case"openDialog":return U(U({},Z),{},{isFileDialogActive:!0});case"closeDialog":return U(U({},e),{},{isFileDialogActive:!1});case"setDraggedFiles":return U(U({},e),{},{isDragActive:t.isDragActive,isDragAccept:t.isDragAccept,isDragReject:t.isDragReject});case"setFiles":return U(U({},e),{},{acceptedFiles:t.acceptedFiles,fileRejections:t.fileRejections,isDragReject:t.isDragReject});case"reset":return U({},Z);default:return e}}function et(){}},47956:(e,t,a)=>{"use strict";a.d(t,{Q:()=>i});var i=(0,a(69478).tv)({base:[],variants:{orientation:{vertical:["overflow-y-auto","data-[top-scroll=true]:[mask-image:linear-gradient(0deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[bottom-scroll=true]:[mask-image:linear-gradient(180deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[top-bottom-scroll=true]:[mask-image:linear-gradient(#000,#000,transparent_0,#000_var(--scroll-shadow-size),#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]"],horizontal:["overflow-x-auto","data-[left-scroll=true]:[mask-image:linear-gradient(270deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[right-scroll=true]:[mask-image:linear-gradient(90deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[left-right-scroll=true]:[mask-image:linear-gradient(to_right,#000,#000,transparent_0,#000_var(--scroll-shadow-size),#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]"]},hideScrollBar:{true:"scrollbar-hide",false:""}},defaultVariants:{orientation:"vertical",hideScrollBar:!1}})},72948:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},74409:(e,t,a)=>{"use strict";a.d(t,{y:()=>i});var i=a(77927).q},79399:(e,t,a)=>{"use strict";var i=a(72948);function n(){}function o(){}o.resetWarningCache=n,e.exports=function(){function e(e,t,a,n,o,l){if(l!==i){var r=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw r.name="Invariant Violation",r}}function t(){return e}e.isRequired=e;var a={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:n};return a.PropTypes=a,a}},83662:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(40157).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}}]);