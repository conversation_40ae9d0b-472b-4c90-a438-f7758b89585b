(()=>{var e={};e.id=982,e.ids=[982],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4555:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>u,tree:()=>l});var o=r(65239),n=r(48088),a=r(88170),s=r.n(a),i=r(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let l={children:["",{children:["(home)",{children:["googlecallback",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,48492)),"G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\googlecallback\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,89282)),"G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"G:\\Graduation project 2025\\app\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\googlecallback\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(home)/googlecallback/page",pathname:"/googlecallback",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},9544:(e,t,r)=>{Promise.resolve().then(r.bind(r,66378))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22696:(e,t,r)=>{Promise.resolve().then(r.bind(r,48492))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48492:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\googlecallback\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app\\frontend\\src\\app\\(home)\\googlecallback\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66378:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var o=r(60687);r(43210);var n=r(16189),a=r(20211);function s(){(0,n.useRouter)();let[,e]=(0,a.lT)();return(0,n.useSearchParams)().get("code")?(0,o.jsxs)("div",{className:"grid place-content-center h-full",children:[(0,o.jsx)("p",{className:"font-bold text-2xl",children:"جارى تسجيل الدخول..."}),";"]}):(0,n.notFound)()}r(52581)},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,310,326,526,160,518],()=>r(4555));module.exports=o})();